{"name": "auto-jump-check", "module": "index.ts", "type": "module", "private": true, "scripts": {"test": "bun run test.ts", "test:simple": "bun run simple-test.ts", "example:basic": "bun run examples/basic.ts", "example:advanced": "bun run examples/advanced.ts", "dev": "bun run --watch src/index.ts", "build": "bun build src/index.ts --outdir dist --target bun", "start": "bun run src/index.ts", "demo": "bun run demo-complete.ts"}, "devDependencies": {"@types/bun": "latest", "@types/node": "^24.5.2"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"p-queue": "^8.1.1", "pino": "^9.10.0", "playwright": "^1.55.0"}}