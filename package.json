{"name": "auto-jump-check", "module": "index.ts", "type": "module", "private": true, "scripts": {"test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "build": "bun build src/index.ts --outdir dist --target bun"}, "devDependencies": {"@types/bun": "latest", "@types/node": "^24.5.2", "@vitest/ui": "^3.2.4", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"p-queue": "^8.1.1", "pino": "^9.10.0", "playwright": "^1.55.0"}}