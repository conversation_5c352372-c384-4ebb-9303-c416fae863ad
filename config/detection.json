{"timeout": {"pageLoad": 10000, "redirectDetection": 6000, "networkIdle": 2000, "totalTask": 15000, "timerRedirect": 8000, "metaRefresh": 3000, "formSubmit": 5000}, "resourceFilter": {"interceptResourceTypes": ["document", "stylesheet", "script", "xhr", "fetch", "other"], "allowedResourceTypes": ["image", "media", "font", "websocket"], "domainWhitelist": [], "blockThirdParty": false}, "concurrent": {"maxConcurrent": 5, "queueSize": 100, "taskTimeout": 60000, "retryCount": 3}, "enableScreenshots": false, "enableNetworkLogging": false, "debugMode": false}