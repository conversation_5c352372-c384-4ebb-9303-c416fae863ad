构建一个基于 Bun 的自动跳转检测系统，用于检测网页中的各种跳转行为。系统需要模拟真实浏览器环境并支持并发处理。

## 技术栈要求
- **运行时环境**：Bun (最新稳定版本)
- **本地服务器**：使用 `Bun.serve()` 搭建本地静态文件服务器，用于托管 HTML 素材
- **浏览器自动化**：Playwright (直接使用 playwright 包，不要使用 @playwright/test)
- **并发处理**：使用内置队列管理机制或 p-queue 库
- **类型安全**：TypeScript 支持

## 核心功能实现

### 1. HTML素材加载机制
- 使用 `Bun.serve()` 创建本地 HTTP 服务器，专门用于托管 HTML 素材文件
- 确保在真实的 HTTP 环境中进行检测，避免 about:blank 环境的限制
- 支持动态创建临时 HTML 文件并自动清理
- 配置适当的 MIME 类型和缓存策略
- 实现优雅关闭机制，处理 SIGINT 和 SIGTERM 信号

### 3. 设备模拟配置
**主要目标设备**：
- 预定义移动端设备配置：
  - **iOS Safari**:
    - iPhone 13: `390x844`, deviceScaleFactor: 3.0, User-Agent: 最新 iOS Safari
    - iPhone 14: `393x852`, deviceScaleFactor: 3.0, User-Agent: 最新 iOS Safari
    - iPhone 15: `393x852`, deviceScaleFactor: 3.0, User-Agent: 最新 iOS Safari
  - **Android Chrome**:
    - Samsung Galaxy S21: `360x800`, deviceScaleFactor: 3.0, User-Agent: 最新 Android Chrome
    - Google Pixel 7: `412x892`, deviceScaleFactor: 2.625, User-Agent: 最新 Android Chrome
    - Xiaomi 13: `412x915`, deviceScaleFactor: 2.75, User-Agent: 最新 Android Chrome

**可选支持设备**：
- iPad: `820x1180`, deviceScaleFactor: 2.0, User-Agent: 最新 iPad Safari
- 桌面端浏览器: Chrome、Firefox、Safari、Edge 最新版本

**设备配置模板**：
```javascript
// 移动端设备配置示例
const mobileDeviceConfig = {
  // iOS Safari 配置
  iPhone15: {
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
    viewport: { width: 393, height: 852 },
    deviceScaleFactor: 3.0,
    isMobile: true,
    hasTouch: true,
    defaultBrowserType: 'webkit'
  },
  // Android Chrome 配置
  Pixel7: {
    userAgent: 'Mozilla/5.0 (Linux; Android 13; Pixel 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
    viewport: { width: 412, height: 892 },
    deviceScaleFactor: 2.625,
    isMobile: true,
    hasTouch: true,
    defaultBrowserType: 'chromium'
  }
}
```

**自定义设备参数**：
- 支持自定义视口大小、设备像素比例
- 支持自定义 User-Agent 字符串
- 支持选择浏览器类型（chromium、firefox、webkit）

### 4. 环境模拟可配置化
**用户代理（User-Agent）自定义**：
- 支持从预定义的 User-Agent 池中选择
- 支持自定义 User-Agent 字符串
- 支持 User-Agent 轮换机制，避免被识别为自动化工具

**网络条件模拟**：
- 支持自定义网络速度（慢速3G、快速3G、4G、WiFi等）
- 支持网络延迟模拟（0ms-5000ms可配置）
- 支持网络丢包率模拟（0%-10%可配置）
- 支持离线模式模拟
```javascript
// 网络条件配置示例
const networkConditions = {
  offline: false,
  downloadThroughput: 500 * 1024, // 500KB/s
  uploadThroughput: 500 * 1024,   // 500KB/s
  latency: 20                    // 20ms
}
```

**地理位置和时区设置**：
- 支持自定义地理位置（经纬度坐标）
- 预设主要城市位置（北京、上海、广州、深圳等）
- 支持时区设置（默认：Asia/Shanghai）
- 支持语言和区域设置（默认：zh-CN）

**屏幕分辨率和像素密度配置**：
- 支持自定义视口大小
- 支持设备像素比例（deviceScaleFactor）设置
- 支持屏幕方向（横屏/竖屏）模拟
- 支持触屏设备模拟

**环境配置示例**：
```javascript
// 完整的环境配置示例
const environmentConfig = {
  userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
  viewport: { width: 393, height: 852 },
  deviceScaleFactor: 3.0,
  locale: 'zh-CN',
  timezoneId: 'Asia/Shanghai',
  geolocation: { latitude: 39.9042, longitude: 116.4074 },
  networkConditions: {
    offline: false,
    downloadThroughput: 500 * 1024,
    uploadThroughput: 500 * 1024,
    latency: 20
  },
  permissions: ['geolocation']
}
```

### 5. 跳转监听机制完善
**专注检测自动跳转行为**（按检测优先级排序）：
1. **JavaScript 定时器触发的跳转**：
   - `setTimeout(() => location.href = url, delay)`
   - `setInterval` 中的跳转逻辑
   - 使用 `requestAnimationFrame` 实现的延迟跳转

2. **程序化的 location 修改**：
   - `window.location.href = url`
   - `window.location.replace(url)`
   - `window.location.assign(url)`
   - `window.open(url, '_self')`

3. **模拟的 a 标签点击事件**：
   - 程序化触发的 `a.click()` 事件
   - 通过 `dispatchEvent` 模拟的点击行为

4. **meta refresh 标签**：
   - `<meta http-equiv="refresh" content="0;url=...">`
   - 带延迟的 meta refresh：`<meta http-equiv="refresh" content="5;url=...">`

5. **表单自动提交**：
   - 页面加载完成后自动提交的表单
   - 通过 JavaScript 触发的表单提交

**排除用户主动触发的跳转行为**：
- 不检测用户真实点击的链接跳转
- 不检测用户手动提交的表单
- 不检测用户通过地址栏直接输入的导航

**实现方式**：
- 页面加载前注入监听脚本：使用 `page.addInitScript()`
- 使用 Proxy 拦截 `window.location` 对象的所有操作
- 重写 `setTimeout`、`setInterval` 等定时器方法
- 监听 Playwright 的 `page.on('framenavigated')` 事件
- 设置 `page.route()` 拦截所有导航请求
- 记录跳转触发的精确时间戳和调用栈信息
- 区分程序化触发和用户触发的跳转行为

### 6. 资源过滤规则
**仅拦截和分析当前域名下的资源请求**：
- 自动识别当前页面的主域名
- 仅拦截和分析与主域名同源或子域名的资源请求
- 对第三方域名（CDN、广告、统计等）不进行拦截，避免误判和干扰

**资源拦截策略**：
```javascript
// 资源过滤配置示例
const resourceFilterConfig = {
  // 仅拦截以下类型的资源
  interceptResourceTypes: [
    'document', 'stylesheet', 'script', 'xhr', 'fetch', 'other'
  ],
  
  // 不拦截的资源类型
  allowedResourceTypes: [
    'image', 'media', 'font', 'websocket'
  ],
  
  // 域名白名单配置
  domainWhitelist: [
    // 当前域名及其子域名会自动加入白名单
    // 可手动添加需要特殊处理的第三方域名
  ],
  
  // 是否拦截第三方域名请求
  blockThirdParty: false
};
```

**域名白名单配置**：
- 支持通配符配置（如 `*.example.com`）
- 支持正则表达式匹配
- 支持动态添加和移除域名规则
- 提供预设的常见 CDN 和公共服务域名白名单

**实现方式**：
- 使用 `page.route()` 设置请求拦截器
- 在拦截器中判断请求域名是否与当前域名匹配
- 对匹配的请求进行详细分析和记录
- 对不匹配的请求直接放行

**超时配置**：
- 页面导航超时：10 秒 (`page.goto(url, { timeout: 10000 })`)
- 跳转等待时间：6 秒（检测到跳转后的额外等待）
- 网络空闲等待：2 秒 (`waitUntil: 'networkidle'`)
- 单个任务最大执行时间：15 秒

### 2. 并发处理优化
- 使用 `p-queue` 库实现任务队列管理，支持Playwright多页面并行处理
- 配置参数：
  - 最大并发数：5（可通过 `MAX_CONCURRENT` 环境变量配置）
  - 队列大小限制：100 个待处理任务
  - 任务超时：60 秒
  - 重试次数：3 次，指数退避策略
- 实现并发数量配置选项，以平衡性能和资源消耗
- 支持批量处理多个HTML素材文件，每个文件使用独立的浏览器上下文
- 提供任务进度监控和资源使用情况统计

### 7. 检测结果数据结构

**标准化输出格式**：
- 移除 REST API 接口相关设计和实现
- 保留检测结果的标准化数据结构输出
- 结果输出格式应包含：检测状态、跳转详情、时间戳、设备信息等

**检测结果数据结构**：
```typescript
interface DetectionResult {
  // 检测状态
  status: 'success' | 'failed' | 'timeout';
  
  // 跳转详情
  redirectInfo?: {
    hasRedirect: boolean;
    redirectType?: 'timer' | 'location' | 'meta' | 'form' | 'simulated_click';
    targetUrl?: string;
    triggerTime?: number; // 跳转触发时间（毫秒）
    redirectDelay?: number; // 跳转延迟时间（毫秒）
    sourceCode?: string; // 触发跳转的源代码片段
  };
  
  // 时间信息
  timestamps: {
    startTime: number; // 检测开始时间戳
    endTime: number; // 检测结束时间戳
    pageLoadTime?: number; // 页面加载时间（毫秒）
    totalTime: number; // 总检测时间（毫秒）
  };
  
  // 设备信息
  deviceInfo: {
    type: 'mobile' | 'tablet' | 'desktop';
    browser: 'chrome' | 'safari' | 'firefox';
    userAgent: string;
    viewport: {
      width: number;
      height: number;
    };
    deviceScaleFactor: number;
  };
  
  // 环境配置
  environment?: {
    locale: string;
    timezone: string;
    geolocation?: {
      latitude: number;
      longitude: number;
    };
    networkConditions?: {
      offline: boolean;
      downloadThroughput?: number;
      uploadThroughput?: number;
      latency?: number;
    };
  };
  
  // 错误信息
  errors?: Array<{
    type: 'NETWORK_ERROR' | 'TIMEOUT_ERROR' | 'PARSE_ERROR' | 'BROWSER_ERROR' | 'VALIDATION_ERROR';
    message: string;
    stack?: string;
    timestamp: number;
  }>;
  
  // 可选截图数据
  screenshots?: {
    before?: string; // Base64 编码的跳转前截图
    after?: string; // Base64 编码的跳转后截图
  };
  
  // 网络请求记录（仅限当前域名）
  networkRequests?: Array<{
    url: string;
    method: string;
    status: number;
    timestamp: number;
    resourceType: string;
  }>;
}
```

**结果输出方式**：
- 支持控制台输出（JSON 格式）
- 支持文件输出（JSON 格式）
- 支持自定义回调函数处理结果
- 支持批量检测结果汇总输出

### 8. 项目目录结构重新规划

**根据功能改动重新设计的项目文件组织结构**：
```
auto-jump-check/
├── src/
│   ├── core/
│   │   ├── index.ts                              // 主入口文件
│   │   ├── detector.ts                           // 跳转检测核心逻辑
│   │   └── types.ts                              // TypeScript 类型定义
│   ├── server/
│   │   ├── index.ts                              // 静态文件服务器
│   │   └── file-manager.ts                       // 文件管理器
│   ├── browser/
│   │   ├── manager.ts                            // 浏览器实例管理
│   │   ├── devices.ts                            // 设备配置
│   │   └── environment.ts                        // 环境模拟配置
│   ├── detection/
│   │   ├── listeners.ts                          // 跳转监听器
│   │   ├── filters.ts                            // 资源过滤器
│   │   └── analyzer.ts                           // 结果分析器
│   ├── concurrent/
│   │   ├── queue-manager.ts                      // 队列管理器
│   │   ├── task-executor.ts                      // 任务执行器
│   │   └── resource-monitor.ts                   // 资源监控器
│   ├── output/
│   │   ├── formatter.ts                          // 结果格式化器
│   │   ├── console.ts                            // 控制台输出
│   │   └── file.ts                               // 文件输出
│   └── utils/
│       ├── logger.ts                             // 日志工具
│       ├── helpers.ts                            // 辅助函数
│       └── config.ts                             // 配置管理
├── config/
│   ├── devices.json                              // 设备配置
│   ├── environments.json                         // 环境配置
│   └── detection.json                            // 检测配置
├── templates/
│   ├── html/                                     // HTML 模板
│   └── scripts/                                  // 注入脚本模板
├── tests/
│   ├── unit/                                     // 单元测试
│   ├── integration/                              // 集成测试
│   ├── fixtures/                                 // 测试数据
│   └── html-samples/                             // 测试用 HTML 样本
├── docs/
│   ├── USAGE.md                                  // 使用说明
│   ├── CONFIGURATION.md                          // 配置说明
│   └── ARCHITECTURE.md                           // 架构说明
├── examples/
│   ├── basic.ts                                  // 基本使用示例
│   ├── advanced.ts                               // 高级配置示例
│   └── batch.ts                                  // 批量处理示例
├── package.json
├── tsconfig.json
├── bun.lockb
└── README.md
```

**各模块职责分工**：
- **core**: 核心逻辑和类型定义，提供统一的接口
- **server**: 静态文件服务器，负责托管 HTML 素材
- **browser**: 浏览器管理和环境模拟，包括设备配置
- **detection**: 跳转检测相关逻辑，包括监听器和过滤器
- **concurrent**: 并发处理相关，包括队列管理和资源监控
- **output**: 结果输出处理，包括格式化和多种输出方式
- **utils**: 通用工具函数和配置管理

**模块依赖关系**：
- core 依赖所有其他模块，提供统一入口
- detection 依赖 browser 和 utils
- concurrent 依赖 browser 和 detection
- output 依赖 core 和 utils
- server 独立运行，为 browser 提供素材服务

## 技术挑战和限制

### 1. 核心技术难点

**区分自动跳转和用户主动触发的跳转**：
- 虽然方案中提到了要排除用户主动触发的跳转行为，但实际实现中可能很难准确区分，特别是在复杂的交互场景中
- 需要通过事件时间戳比较、`isTrusted` 属性检查和调用栈分析来实现准确判断
- 对于模拟点击事件，需要特别注意区分程序化触发和真实用户操作

**处理复杂的定时器跳转**：
- 有些跳转可能嵌套在多个定时器或异步回调中，难以准确捕获
- 需要深度拦截 `setTimeout`、`setInterval`、`requestAnimationFrame` 等异步执行方法
- 对于 Promise、async/await 等现代异步模式也需要特殊处理

**处理动态生成的跳转代码**：
- 有些跳转行为可能是通过 `eval` 或 `Function` 构造函数动态生成的，这会增加检测的难度
- 需要拦截动态代码执行，并分析其中可能包含的跳转逻辑
- 对于通过字符串拼接或模板生成的跳转代码需要特殊识别机制

**性能和资源消耗**：
- 同时检测多个页面可能会占用大量内存和 CPU 资源，需要合理配置并发数和超时时间
- 浏览器实例管理需要优化，避免内存泄漏和资源浪费
- 长时间运行的检测任务需要资源监控和自动回收机制

### 2. 浏览器兼容性挑战

**不同浏览器引擎的差异**：
- Chromium、Firefox、WebKit 在跳转行为上可能存在细微差异
- 需要针对不同浏览器引擎调整检测策略
- 移动端浏览器的特殊行为需要额外考虑

**安全策略限制**：
- 现代浏览器的安全策略可能阻止某些跳转检测方法
- Content Security Policy (CSP) 可能影响脚本注入和执行
- 跨域限制可能影响某些检测功能的实现

## 边界情况分析

### 1. 复杂异步场景

**嵌套定时器或异步回调中的跳转**：
- 需要考虑在多层嵌套的定时器或异步回调中触发的跳转行为
- 处理 Promise 链、async/await 中的跳转逻辑
- 识别通过 Web Workers 或 Service Workers 触发的跳转

**通过 Object.defineProperty 重新定义 location 对象**：
- 需要考虑通过 JavaScript 重新定义 location 对象的情况
- 处理 Proxy 对象包装的 location 访问
- 检测通过 getter/setter 实现的延迟跳转逻辑

### 2. 特殊 DOM 环境

**通过 Shadow DOM 或 iframe 中的元素触发的跳转**：
- 需要考虑在 Shadow DOM 或 iframe 中的元素触发的跳转行为
- 处理跨 iframe 的跳转检测和事件监听
- 识别 Shadow DOM 内部的自动跳转逻辑

**动态创建的 DOM 元素跳转**：
- 检测通过 JavaScript 动态创建并自动触发的链接或表单
- 处理通过 MutationObserver 监听到的 DOM 变化引起的跳转
- 识别通过 document.write() 注入的跳转代码

### 3. 网络和表单处理

**AJAX 提交表单后重定向**：
- 需要考虑通过 AJAX 提交表单后重定向的情况
- 处理 fetch API 和 XMLHttpRequest 的响应重定向
- 识别服务端返回的跳转指令（如 HTTP 302/301 响应）

**WebSocket 连接触发的跳转或页面刷新**：
- 需要考虑 WebSocket 连接可能触发的跳转或页面刷新行为
- 处理实时消息推送导致的页面跳转
- 监听 Server-Sent Events (SSE) 可能触发的跳转

### 4. 现代 Web 技术场景

**Single Page Application (SPA) 路由跳转**：
- 区分 SPA 内部路由跳转和真实页面跳转
- 处理 History API (pushState/replaceState) 的使用
- 识别基于 hash 的路由跳转

**Progressive Web App (PWA) 特殊行为**：
- 处理 PWA 的离线缓存和后台同步可能引起的跳转
- 识别通过 App Manifest 配置的启动行为
- 处理 Service Worker 缓存策略对跳转检测的影响

## 需要特别考虑的技术细节

### 1. 跳转检测精度

**准确捕获跳转触发的调用栈信息**：
- 这对于定位跳转源代码非常重要
- 需要在脚本注入时保留完整的调用栈信息
- 实现源码映射(Source Map)支持，便于调试和分析

**跳转时机的精确记录**：
- 记录跳转触发的精确时间戳（高精度时间）
- 区分跳转开始时间和实际导航发生时间
- 计算跳转延迟和执行时间

### 2. 跨域和安全处理

**处理跨域跳转**：
- 特别是当跳转目标不在白名单中时的处理策略
- 实现跨域跳转的安全检查和记录
- 处理 CORS 策略对跳转检测的影响

**处理 HTTPS 和 HTTP 混合内容**：
- 需要考虑混合内容对跳转检测的影响
- 处理安全上下文降级可能导致的跳转失败
- 实现协议升级检测和处理

### 3. 浏览器环境适配

**处理浏览器扩展或插件的影响**：
- 需要考虑浏览器扩展或插件可能对页面跳转的影响
- 识别和过滤由扩展引起的跳转行为
- 处理广告拦截器等工具对检测的干扰

**处理页面卸载前的跳转行为**：
- 特别是 `beforeunload` 事件的处理
- 监听 `pagehide`、`unload` 等页面生命周期事件
- 处理页面卸载过程中的异步跳转

### 4. Service Worker 和缓存

**处理 Service Worker 拦截的导航请求**：
- 需要考虑 Service Worker 可能拦截的导航请求
- 识别通过 Service Worker 实现的离线跳转
- 处理缓存策略对跳转检测的影响

## 改进建议

### 1. 动态代码检测增强

**增加对动态代码执行的检测**：
- 拦截 `eval()` 函数调用，分析执行的代码内容
- 监听 `Function` 构造函数的使用，检测动态生成的函数
- 实现代码字符串分析，识别可能的跳转逻辑
- 添加对 `setTimeout`/`setInterval` 字符串参数的特殊处理

```javascript
// 动态代码检测示例
const originalEval = window.eval;
window.eval = function(code) {
  // 分析代码内容，检测跳转相关操作
  if (typeof code === 'string' && /location\.|window\.open|href\s*=/.test(code)) {
    console.log('检测到动态跳转代码:', code);
  }
  return originalEval.call(this, code);
};
```

### 2. DOM 监听范围扩展

**增加对 Shadow DOM 和 iframe 中元素的监听**：
- 实现 Shadow DOM 内部的事件监听和跳转检测
- 扩展 iframe 跨域通信机制，监听子框架中的跳转
- 添加 MutationObserver 监听，检测动态创建的跳转元素

```javascript
// Shadow DOM 监听示例
function observeShadowDOM(root) {
  const observer = new MutationObserver(mutations => {
    mutations.forEach(mutation => {
      mutation.addedNodes.forEach(node => {
        if (node.shadowRoot) {
          setupRedirectListeners(node.shadowRoot);
        }
      });
    });
  });
  observer.observe(root, { childList: true, subtree: true });
}
```

### 3. AJAX 重定向检测

**增加对 AJAX 请求后重定向的检测机制**：
- 拦截 `fetch` 和 `XMLHttpRequest` 的响应处理
- 检测服务端返回的重定向指令和 JavaScript 重定向代码
- 监听响应头中的 `Location` 字段和状态码

```javascript
// AJAX 重定向检测示例
const originalFetch = window.fetch;
window.fetch = function(...args) {
  return originalFetch.apply(this, args).then(response => {
    if (response.redirected || [301, 302, 303, 307, 308].includes(response.status)) {
      console.log('检测到 AJAX 重定向:', response.url);
    }
    return response;
  });
};
```

### 4. 超时策略优化

**增加更灵活的超时配置**：
- 允许针对不同类型的跳转设置不同的超时时间
- 实现自适应超时机制，根据页面复杂度动态调整
- 添加超时前的警告机制和优雅降级处理

```javascript
// 灵活超时配置示例
const timeoutConfig = {
  pageLoad: 10000,        // 页面加载超时
  redirectDetection: 6000, // 跳转检测超时
  networkIdle: 2000,      // 网络空闲超时
  totalTask: 15000,       // 总任务超时
  // 针对特定跳转类型的超时配置
  timerRedirect: 8000,    // 定时器跳转超时
  metaRefresh: 3000,      // meta refresh 超时
  formSubmit: 5000        // 表单提交超时
};
```

## 实际应用场景考虑

### 1. 用户交互识别优化

**区分用户交互跳转和自动跳转**：
- 通过事件时间戳比较识别用户操作：检查事件触发时间与页面加载时间的间隔
- 使用 `isTrusted` 属性检查：真实用户事件的 `isTrusted` 为 `true`
- 分析事件冒泡路径：用户操作通常有完整的事件冒泡链
- 检测鼠标/触摸轨迹：真实用户操作有连续的移动轨迹

```javascript
// 用户交互识别示例
function isUserInitiated(event) {
  return event.isTrusted && 
         (Date.now() - pageLoadTime) > 1000 && // 页面加载后至少1秒
         event.detail > 0; // 有用户交互细节
}
```

### 2. 分阶段超时策略

**设置分阶段的超时策略**：
建议采用分阶段的超时策略以适应不同类型的跳转检测需求：

```javascript
// 分阶段超时策略配置
const phaseTimeoutConfig = {
  // 第一阶段：页面初始加载
  phase1: {
    name: '页面加载',
    timeout: 10000, // 10秒
    description: '等待页面完全加载和初始脚本执行'
  },
  
  // 第二阶段：跳转检测等待
  phase2: {
    name: '跳转检测',
    timeout: 6000, // 6秒
    description: '检测页面加载后可能发生的自动跳转'
  },
  
  // 第三阶段：网络空闲等待
  phase3: {
    name: '网络空闲',
    timeout: 2000, // 2秒
    description: '等待网络请求完成，确保没有延迟的跳转'
  },
  
  // 总任务超时保护
  totalTimeout: 15000, // 15秒
  
  // 特殊场景的超时配置
  specialCases: {
    metaRefresh: 3000,    // meta refresh 标签
    timerRedirect: 8000,  // 定时器跳转
    ajaxRedirect: 5000,   // AJAX 重定向
    formSubmit: 4000      // 表单自动提交
  }
};
```

### 3. 智能检测策略

**基于页面特征的智能检测**：
- 分析页面内容，识别可能包含跳转逻辑的脚本
- 根据页面类型（落地页、中转页等）调整检测策略
- 实现机器学习辅助的跳转模式识别

### 4. 错误恢复机制

**完善的错误处理和恢复**：
- 实现超时后的页面状态检查和恢复
- 添加网络错误的重试机制
- 提供检测失败时的降级方案

## 错误处理和日志
- **错误分类**：
  - `NETWORK_ERROR`: 网络连接失败
  - `TIMEOUT_ERROR`: 操作超时
  - `PARSE_ERROR`: HTML/URL 解析错误
  - `BROWSER_ERROR`: 浏览器操作失败
  - `VALIDATION_ERROR`: 输入参数验证失败
  - `DETECTION_ERROR`: 跳转检测逻辑错误
  - `DYNAMIC_CODE_ERROR`: 动态代码执行错误
- **日志级别**：使用 `pino` 日志库，支持 `trace`, `debug`, `info`, `warn`, `error`, `fatal`
- **调试模式**：通过 `DEBUG=true` 环境变量启用，输出详细执行过程
- **截图功能**：在跳转前后自动截图，支持全页面截图和视窗截图
- **性能监控**：记录内存使用、CPU 占用、任务执行时间等指标
- **调用栈追踪**：记录跳转触发的完整调用栈，便于问题定位

请基于以上详细规范实现这个自动跳转检测系统，确保代码质量、类型安全和可维护性。实现时请遵循 TypeScript 最佳实践，添加完整的错误处理和单元测试。特别注意处理上述提到的技术挑战和边界情况，确保系统在复杂环境下的稳定性和准确性。
