/**
 * 文件管理器
 */

import { randomUUID } from 'crypto';
import { existsSync, mkdirSync, rmSync, unlinkSync, writeFileSync } from 'fs';
import { basename, dirname, extname, join } from 'path';
import type { IFileManager } from '../core/types';

export class FileManager implements IFileManager {
  private tempDir: string;
  private createdFiles: Set<string> = new Set();

  constructor(tempDir: string = './temp') {
    this.tempDir = tempDir;
    this.ensureTempDir();
  }

  /**
   * 确保临时目录存在
   */
  private ensureTempDir(): void {
    if (!existsSync(this.tempDir)) {
      mkdirSync(this.tempDir, { recursive: true });
    }
  }

  /**
   * 创建临时文件
   */
  async createTempFile(content: string, extension: string = '.html'): Promise<string> {
    this.ensureTempDir();

    // 生成唯一文件名
    const fileName = `${randomUUID()}${extension}`;
    const filePath = join(this.tempDir, fileName);

    try {
      // 写入文件内容
      writeFileSync(filePath, content, 'utf-8');

      // 记录创建的文件
      this.createdFiles.add(filePath);

      return filePath;
    } catch (error) {
      throw new Error(`Failed to create temp file: ${error}`);
    }
  }

  /**
   * 创建带有特定名称的临时文件
   */
  async createNamedTempFile(content: string, name: string): Promise<string> {
    this.ensureTempDir();

    const filePath = join(this.tempDir, name);
    const fileDir = dirname(filePath);

    // 确保目录存在
    if (!existsSync(fileDir)) {
      mkdirSync(fileDir, { recursive: true });
    }

    try {
      writeFileSync(filePath, content, 'utf-8');
      this.createdFiles.add(filePath);
      return filePath;
    } catch (error) {
      throw new Error(`Failed to create named temp file: ${error}`);
    }
  }

  /**
   * 删除临时文件
   */
  async deleteTempFile(filePath: string): Promise<void> {
    try {
      if (existsSync(filePath)) {
        unlinkSync(filePath);
      }
      this.createdFiles.delete(filePath);
    } catch (error) {
      console.warn(`Failed to delete temp file ${filePath}:`, error);
    }
  }

  /**
   * 批量创建临时文件
   */
  async createTempFiles(
    files: Array<{ content: string; name?: string; extension?: string }>
  ): Promise<string[]> {
    const createdFiles: string[] = [];

    for (const file of files) {
      try {
        let filePath: string;

        if (file.name) {
          filePath = await this.createNamedTempFile(file.content, file.name);
        } else {
          filePath = await this.createTempFile(file.content, file.extension);
        }

        createdFiles.push(filePath);
      } catch (error) {
        // 如果创建失败，清理已创建的文件
        for (const createdFile of createdFiles) {
          await this.deleteTempFile(createdFile);
        }
        throw error;
      }
    }

    return createdFiles;
  }

  /**
   * 获取文件的相对路径（相对于临时目录）
   */
  getRelativePath(filePath: string): string {
    if (filePath.startsWith(this.tempDir)) {
      return filePath.substring(this.tempDir.length + 1);
    }
    return basename(filePath);
  }

  /**
   * 获取文件的完整路径
   */
  getFullPath(relativePath: string): string {
    return join(this.tempDir, relativePath);
  }

  /**
   * 检查文件是否存在
   */
  fileExists(filePath: string): boolean {
    return existsSync(filePath);
  }

  /**
   * 获取文件信息
   */
  getFileInfo(filePath: string): { name: string; extension: string; relativePath: string } | null {
    if (!this.fileExists(filePath)) {
      return null;
    }

    return {
      name: basename(filePath, extname(filePath)),
      extension: extname(filePath),
      relativePath: this.getRelativePath(filePath),
    };
  }

  /**
   * 列出所有创建的临时文件
   */
  listCreatedFiles(): string[] {
    return Array.from(this.createdFiles);
  }

  /**
   * 清理所有临时文件
   */
  async cleanup(): Promise<void> {
    const filesToDelete = Array.from(this.createdFiles);

    for (const filePath of filesToDelete) {
      await this.deleteTempFile(filePath);
    }

    // 尝试删除临时目录（如果为空）
    try {
      if (existsSync(this.tempDir)) {
        rmSync(this.tempDir, { recursive: true, force: true });
      }
    } catch (error) {
      console.warn(`Failed to remove temp directory ${this.tempDir}:`, error);
    }

    this.createdFiles.clear();
  }

  /**
   * 获取临时目录路径
   */
  getTempDir(): string {
    return this.tempDir;
  }

  /**
   * 设置新的临时目录
   */
  setTempDir(newTempDir: string): void {
    this.tempDir = newTempDir;
    this.ensureTempDir();
  }

  /**
   * 创建HTML测试文件的辅助方法
   */
  async createHtmlTestFile(
    title: string,
    bodyContent: string,
    scripts: string[] = [],
    styles: string[] = []
  ): Promise<string> {
    const scriptTags = scripts.map((script) => `<script>${script}</script>`).join('\n');
    const styleTags = styles.map((style) => `<style>${style}</style>`).join('\n');

    const htmlContent = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    ${styleTags}
</head>
<body>
    ${bodyContent}
    ${scriptTags}
</body>
</html>`;

    return await this.createTempFile(htmlContent, '.html');
  }

  /**
   * 创建包含跳转逻辑的测试HTML文件
   */
  async createRedirectTestFile(
    redirectType: 'timer' | 'location' | 'meta' | 'form' | 'click',
    targetUrl: string = 'https://example.com',
    delay: number = 2000
  ): Promise<string> {
    let bodyContent = '';
    let scripts: string[] = [];

    switch (redirectType) {
      case 'timer':
        bodyContent = '<h1>定时器跳转测试</h1><p>页面将在2秒后自动跳转...</p>';
        scripts.push(
          `setTimeout(function() { window.location.href = '${targetUrl}'; }, ${delay});`
        );
        break;

      case 'location':
        bodyContent = '<h1>Location跳转测试</h1><p>页面将立即跳转...</p>';
        scripts.push(`window.location.href = '${targetUrl}';`);
        break;

      case 'meta':
        return await this.createTempFile(
          `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="refresh" content="${delay / 1000};url=${targetUrl}">
    <title>Meta Refresh跳转测试</title>
</head>
<body>
    <h1>Meta Refresh跳转测试</h1>
    <p>页面将在${delay / 1000}秒后自动跳转...</p>
</body>
</html>`,
          '.html'
        );

      case 'form':
        bodyContent = `<h1>表单跳转测试</h1>
<form id="autoForm" action="${targetUrl}" method="get">
    <input type="hidden" name="test" value="auto">
    <p>表单将自动提交...</p>
</form>`;
        scripts.push(
          `setTimeout(function() { document.getElementById('autoForm').submit(); }, ${delay});`
        );
        break;

      case 'click':
        bodyContent = `<h1>模拟点击跳转测试</h1>
<a id="autoLink" href="${targetUrl}">自动点击链接</a>
<p>链接将被自动点击...</p>`;
        scripts.push(
          `setTimeout(function() { document.getElementById('autoLink').click(); }, ${delay});`
        );
        break;
    }

    return await this.createHtmlTestFile(`${redirectType}跳转测试`, bodyContent, scripts);
  }
}
