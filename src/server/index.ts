/**
 * 静态文件服务器
 */

import { type Server } from 'bun';
import { existsSync, statSync } from 'fs';
import { extname, join } from 'path';
import type { ServerConfig } from '../core/types';

export class StaticFileServer {
  private server: Server | null = null;
  private config: ServerConfig;

  constructor(config: Partial<ServerConfig> = {}) {
    this.config = {
      port: config.port || 3000,
      host: config.host || 'localhost',
      staticDir: config.staticDir || './temp',
    };
  }

  /**
   * 启动服务器
   */
  async start(): Promise<void> {
    this.server = Bun.serve({
      port: this.config.port,
      hostname: this.config.host,
      fetch: this.handleRequest.bind(this),
      error: this.handleError.bind(this),
    });

    console.log(`Static file server started at http://${this.config.host}:${this.config.port}`);
  }

  /**
   * 停止服务器
   */
  async stop(): Promise<void> {
    if (this.server) {
      this.server.stop();
      this.server = null;
      console.log('Static file server stopped');
    }
  }

  /**
   * 获取服务器URL
   */
  getBaseUrl(): string {
    return `http://${this.config.host}:${this.config.port}`;
  }

  /**
   * 处理HTTP请求
   */
  private async handleRequest(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const pathname = url.pathname;

    // 处理根路径
    if (pathname === '/') {
      return new Response('Auto Jump Check Server', {
        headers: { 'Content-Type': 'text/plain' },
      });
    }

    // 构建文件路径
    const filePath = join(this.config.staticDir, pathname);

    // 检查文件是否存在
    if (!existsSync(filePath)) {
      return new Response('File not found', {
        status: 404,
        headers: { 'Content-Type': 'text/plain' },
      });
    }

    // 检查是否为文件
    const stats = statSync(filePath);
    if (!stats.isFile()) {
      return new Response('Not a file', {
        status: 400,
        headers: { 'Content-Type': 'text/plain' },
      });
    }

    try {
      // 读取文件
      const file = Bun.file(filePath);
      const content = await file.arrayBuffer();

      // 获取MIME类型
      const mimeType = this.getMimeType(filePath);

      // 设置响应头
      const headers = new Headers({
        'Content-Type': mimeType,
        'Content-Length': content.byteLength.toString(),
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      });

      // 如果是HTML文件，添加额外的安全头
      if (mimeType === 'text/html') {
        headers.set('X-Frame-Options', 'SAMEORIGIN');
        headers.set('X-Content-Type-Options', 'nosniff');
      }

      return new Response(content, {
        status: 200,
        headers,
      });
    } catch (error) {
      console.error('Error serving file:', error);
      return new Response('Internal server error', {
        status: 500,
        headers: { 'Content-Type': 'text/plain' },
      });
    }
  }

  /**
   * 处理服务器错误
   */
  private handleError(error: Error): Response {
    console.error('Server error:', error);
    return new Response('Internal server error', {
      status: 500,
      headers: { 'Content-Type': 'text/plain' },
    });
  }

  /**
   * 根据文件扩展名获取MIME类型
   */
  private getMimeType(filePath: string): string {
    const ext = extname(filePath).toLowerCase();

    const mimeTypes: Record<string, string> = {
      '.html': 'text/html',
      '.htm': 'text/html',
      '.css': 'text/css',
      '': 'application/javascript',
      on: 'application/json',
      '.png': 'image/png',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.gif': 'image/gif',
      '.svg': 'image/svg+xml',
      '.ico': 'image/x-icon',
      '.txt': 'text/plain',
      '.xml': 'application/xml',
      '.pdf': 'application/pdf',
      '.zip': 'application/zip',
      '.woff': 'font/woff',
      '.woff2': 'font/woff2',
      '.ttf': 'font/ttf',
      '.eot': 'application/vnd.ms-fontobject',
    };

    return mimeTypes[ext] || 'application/octet-stream';
  }

  /**
   * 获取文件的完整URL
   */
  getFileUrl(relativePath: string): string {
    // 确保路径以 / 开头
    const normalizedPath = relativePath.startsWith('/') ? relativePath : `/${relativePath}`;
    return `${this.getBaseUrl()}${normalizedPath}`;
  }

  /**
   * 检查服务器是否正在运行
   */
  isRunning(): boolean {
    return this.server !== null;
  }

  /**
   * 获取服务器配置
   */
  getConfig(): ServerConfig {
    return { ...this.config };
  }

  /**
   * 更新服务器配置（需要重启服务器才能生效）
   */
  updateConfig(newConfig: Partial<ServerConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<boolean> {
    if (!this.isRunning()) {
      return false;
    }

    try {
      const response = await fetch(this.getBaseUrl());
      return response.ok;
    } catch (error) {
      console.error('Health check failed:', error);
      return false;
    }
  }
}
