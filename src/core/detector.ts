/**
 * 重构后的跳转检测核心逻辑
 * 修复类型错误，优化代码结构
 */

import { allDevices } from '../browser/devices';
import { defaultEnvironment } from '../browser/environment';
import { BrowserManager } from '../browser/manager';
import { QueueManager } from '../concurrent/queue-manager';
import { TaskExecutor, type TaskExecutionContext } from '../concurrent/task-executor';
import { FileManager } from '../server/file-manager';
import { StaticFileServer } from '../server/index';
import type {
  BatchDetectionOptions,
  BatchDetectionResult,
  DetectionConfig,
  DetectionOptions,
  DetectionResult,
  IDetector,
} from '../types';

export class AutoJumpDetector implements IDetector {
  private browserManager: BrowserManager;
  private fileServer: StaticFileServer;
  private fileManager: FileManager;
  private queueManager: QueueManager;
  private taskExecutor: TaskExecutor;
  private config: DetectionConfig;
  private isInitialized: boolean = false;

  constructor(config?: Partial<DetectionConfig>) {
    this.config = this.createDefaultConfig(config);

    // 初始化组件
    this.browserManager = new BrowserManager();
    this.fileServer = new StaticFileServer({
      port: 3000,
      host: 'localhost',
      staticDir: './temp',
    });
    this.fileManager = new FileManager('./temp');
    this.queueManager = new QueueManager(this.config.concurrent);

    // 创建任务执行上下文
    const executionContext: TaskExecutionContext = {
      browserManager: this.browserManager,
      queueManager: this.queueManager,
      baseUrl: this.fileServer.getBaseUrl(),
    };

    this.taskExecutor = new TaskExecutor(executionContext, this.config.timeout);
  }

  /**
   * 初始化检测器
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // 启动文件服务器
      await this.fileServer.start();

      // 等待服务器就绪
      const isHealthy = await this.fileServer.healthCheck();
      if (!isHealthy) {
        throw new Error('File server failed to start properly');
      }

      this.isInitialized = true;
      console.log('Auto Jump Detector initialized successfully');
    } catch (error) {
      throw new Error(`Failed to initialize detector: ${error}`);
    }
  }

  /**
   * 检测单个目标
   */
  async detect(options: DetectionOptions): Promise<DetectionResult> {
    await this.ensureInitialized();

    try {
      // 处理HTML内容
      if (options.htmlContent) {
        const tempFile = await this.fileManager.createTempFile(options.htmlContent);
        options.htmlFile = tempFile;
      }

      // 使用默认配置填充缺失的选项
      const fullOptions: DetectionOptions = {
        deviceConfig: options.deviceConfig || allDevices.iPhone15,
        environment: options.environment || defaultEnvironment,
        config: { ...this.config, ...options.config },
        ...options,
      };

      // 执行检测任务
      const result = await this.taskExecutor.executeDetectionTask(fullOptions);

      // 清理临时文件
      if (options.htmlContent && options.htmlFile) {
        await this.fileManager.deleteTempFile(options.htmlFile);
      }

      return result;
    } catch (error) {
      throw new Error(`Detection failed: ${error}`);
    }
  }

  /**
   * 批量检测
   */
  async detectBatch(options: BatchDetectionOptions): Promise<BatchDetectionResult> {
    await this.ensureInitialized();

    try {
      const detectionOptions: DetectionOptions[] = [];

      // 处理HTML文件列表
      if (options.htmlFiles) {
        for (const htmlFile of options.htmlFiles) {
          detectionOptions.push({
            htmlFile,
            deviceConfig: options.deviceConfigs?.[0] || allDevices.iPhone15,
            environment: options.environments?.[0] || defaultEnvironment,
            config: options.config,
          });
        }
      }

      // 处理URL列表
      if (options.urls) {
        for (const url of options.urls) {
          detectionOptions.push({
            url,
            deviceConfig: options.deviceConfigs?.[0] || allDevices.iPhone15,
            environment: options.environments?.[0] || defaultEnvironment,
            config: options.config,
          });
        }
      }

      // 如果指定了多个设备配置，为每个设备创建检测任务
      if (options.deviceConfigs && options.deviceConfigs.length > 1) {
        const originalOptions = [...detectionOptions];
        detectionOptions.length = 0;

        for (const deviceConfig of options.deviceConfigs) {
          for (const originalOption of originalOptions) {
            detectionOptions.push({
              ...originalOption,
              deviceConfig,
            });
          }
        }
      }

      if (detectionOptions.length === 0) {
        throw new Error('No detection targets specified');
      }

      // 执行批量检测
      const results = await this.taskExecutor.executeBatchDetection(detectionOptions);

      // 计算统计信息
      const successful = results.filter(r => r.status === 'success').length;
      const failed = results.length - successful;
      const withRedirects = results.filter(r => r.redirectInfo?.hasRedirect).length;
      const totalTime = Math.max(...results.map(r => r.timestamps.totalTime));

      // 收集所有错误
      const allErrors = results.flatMap(r => r.errors || []);

      return {
        results,
        summary: {
          total: results.length,
          successful,
          failed,
          withRedirects,
          totalTime,
        },
        errors: allErrors.length > 0 ? allErrors : undefined,
      };
    } catch (error) {
      throw new Error(`Batch detection failed: ${error}`);
    }
  }

  /**
   * 检测HTML内容
   */
  async detectHtmlContent(
    htmlContent: string,
    deviceConfig?: any,
    environment?: any
  ): Promise<DetectionResult> {
    return this.detect({
      htmlContent,
      deviceConfig,
      environment,
    });
  }

  /**
   * 检测HTML文件
   */
  async detectHtmlFile(
    htmlFile: string,
    deviceConfig?: any,
    environment?: any
  ): Promise<DetectionResult> {
    return this.detect({
      htmlFile,
      deviceConfig,
      environment,
    });
  }

  /**
   * 检测URL
   */
  async detectUrl(url: string, deviceConfig?: any, environment?: any): Promise<DetectionResult> {
    return this.detect({
      url,
      deviceConfig,
      environment,
    });
  }

  /**
   * 获取队列统计信息
   */
  getQueueStats() {
    return this.queueManager.getStats();
  }

  /**
   * 获取检测器配置
   */
  getConfig(): DetectionConfig {
    return { ...this.config };
  }

  /**
   * 更新检测器配置
   */
  updateConfig(newConfig: Partial<DetectionConfig>): void {
    this.config = { ...this.config, ...newConfig };

    // 更新相关组件的配置
    if (newConfig.concurrent) {
      this.queueManager.updateConfig(newConfig.concurrent);
    }

    if (newConfig.timeout) {
      this.taskExecutor.updateTimeoutConfig(newConfig.timeout);
    }
  }

  /**
   * 暂停检测队列
   */
  pauseQueue(): void {
    this.queueManager.pause();
  }

  /**
   * 恢复检测队列
   */
  resumeQueue(): void {
    this.queueManager.start();
  }

  /**
   * 清空检测队列
   */
  clearQueue(): void {
    this.queueManager.clear();
  }

  /**
   * 销毁检测器
   */
  async destroy(): Promise<void> {
    if (!this.isInitialized) {
      return;
    }

    try {
      // 并行清理所有资源
      const cleanupPromises = [
        this.queueManager.destroy().catch((e) => console.warn('Queue cleanup error:', e)),
        this.browserManager.cleanup().catch((e) => console.warn('Browser cleanup error:', e)),
        this.fileManager.cleanup().catch((e) => console.warn('File cleanup error:', e)),
        this.fileServer.stop().catch((e) => console.warn('Server stop error:', e)),
      ];

      await Promise.all(cleanupPromises);

      this.isInitialized = false;
      console.log('Auto Jump Detector destroyed successfully');

      // 强制垃圾回收（如果可用）
      if (global.gc) {
        global.gc();
      }
    } catch (error) {
      console.error('Error during detector destruction:', error);
    }
  }

  /**
   * 确保检测器已初始化
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }
  }

  /**
   * 创建默认配置
   */
  private createDefaultConfig(config?: Partial<DetectionConfig>): DetectionConfig {
    return {
      timeout: {
        pageLoad: 10000,
        redirectDetection: 6000,
        networkIdle: 2000,
        totalTask: 15000,
        timerRedirect: 8000,
        metaRefresh: 3000,
        formSubmit: 5000,
        ...config?.timeout,
      },
      resourceFilter: {
        interceptResourceTypes: ['document', 'stylesheet', 'script', 'xhr', 'fetch', 'other'],
        allowedResourceTypes: ['image', 'media', 'font', 'websocket'],
        domainWhitelist: [],
        blockThirdParty: false,
        ...config?.resourceFilter,
      },
      concurrent: {
        maxConcurrent: parseInt(process.env.MAX_CONCURRENT || '5'),
        queueSize: 100,
        taskTimeout: 60000,
        retryCount: 3,
        ...config?.concurrent,
      },
      enableScreenshots: config?.enableScreenshots || false,
      enableNetworkLogging: config?.enableNetworkLogging || false,
      debugMode: config?.debugMode || process.env.DEBUG === 'true',
    };
  }
}

/**
 * 创建检测器实例
 */
export function createDetector(config?: Partial<DetectionConfig>): AutoJumpDetector {
  return new AutoJumpDetector(config);
}

/**
 * 便捷函数：检测HTML内容
 */
export async function detectHtmlContent(
  htmlContent: string,
  options?: Omit<DetectionOptions, 'htmlContent'>
): Promise<DetectionResult> {
  const detector = createDetector();

  try {
    await detector.initialize();
    const result = await detector.detect({
      htmlContent,
      ...options,
    });
    return result;
  } finally {
    await detector.destroy();
  }
}

/**
 * 便捷函数：检测HTML文件
 */
export async function detectHtmlFile(
  htmlFile: string,
  options?: Omit<DetectionOptions, 'htmlFile'>
): Promise<DetectionResult> {
  const detector = createDetector();

  try {
    await detector.initialize();
    const result = await detector.detect({
      htmlFile,
      ...options,
    });
    return result;
  } finally {
    await detector.destroy();
  }
}

/**
 * 便捷函数：检测URL
 */
export async function detectUrl(
  url: string,
  options?: Omit<DetectionOptions, 'url'>
): Promise<DetectionResult> {
  const detector = createDetector();

  try {
    await detector.initialize();
    const result = await detector.detect({
      url,
      ...options,
    });
    return result;
  } finally {
    await detector.destroy();
  }
}
