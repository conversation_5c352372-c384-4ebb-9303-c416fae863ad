/**
 * 环境模拟配置
 */

import type { EnvironmentConfig, GeolocationConfig, NetworkCondition } from '../types';

// 预定义的网络条件
export const networkConditions: Record<string, NetworkCondition> = {
  // 离线模式
  offline: {
    offline: true,
    downloadThroughput: 0,
    uploadThroughput: 0,
    latency: 0,
  },

  // 慢速3G
  slow3G: {
    offline: false,
    downloadThroughput: 500 * 1024, // 500KB/s
    uploadThroughput: 500 * 1024, // 500KB/s
    latency: 400, // 400ms
  },

  // 快速3G
  fast3G: {
    offline: false,
    downloadThroughput: 1.6 * 1024 * 1024, // 1.6MB/s
    uploadThroughput: 750 * 1024, // 750KB/s
    latency: 150, // 150ms
  },

  // 4G
  fourG: {
    offline: false,
    downloadThroughput: 4 * 1024 * 1024, // 4MB/s
    uploadThroughput: 3 * 1024 * 1024, // 3MB/s
    latency: 20, // 20ms
  },

  // WiFi
  wifi: {
    offline: false,
    downloadThroughput: 30 * 1024 * 1024, // 30MB/s
    uploadThroughput: 15 * 1024 * 1024, // 15MB/s
    latency: 2, // 2ms
  },
};

// 预定义的地理位置
export const locations: Record<string, GeolocationConfig> = {
  // 中国主要城市
  beijing: { latitude: 39.9042, longitude: 116.4074 },
  shanghai: { latitude: 31.2304, longitude: 121.4737 },
  guangzhou: { latitude: 23.1291, longitude: 113.2644 },
  shenzhen: { latitude: 22.3193, longitude: 114.1694 },
  hangzhou: { latitude: 30.2741, longitude: 120.1551 },
  chengdu: { latitude: 30.5728, longitude: 104.0668 },

  // 国际城市
  newYork: { latitude: 40.7128, longitude: -74.006 },
  london: { latitude: 51.5074, longitude: -0.1278 },
  tokyo: { latitude: 35.6762, longitude: 139.6503 },
  singapore: { latitude: 1.3521, longitude: 103.8198 },
};

// 预定义的完整环境配置
export const environments = {
  // 中国环境
  china: {
    locale: 'zh-CN',
    timezone: 'Asia/Shanghai',
    geolocation: locations.beijing,
    networkCondition: networkConditions.wifi,
  },

  // 美国环境
  usa: {
    locale: 'en-US',
    timezone: 'America/New_York',
    geolocation: locations.newYork,
    networkCondition: networkConditions.wifi,
  },

  // 英国环境
  uk: {
    locale: 'en-GB',
    timezone: 'Europe/London',
    geolocation: locations.london,
    networkCondition: networkConditions.wifi,
  },

  // 日本环境
  japan: {
    locale: 'ja-JP',
    timezone: 'Asia/Tokyo',
    geolocation: locations.tokyo,
    networkCondition: networkConditions.wifi,
  },

  // 新加坡环境
  singapore: {
    locale: 'en-SG',
    timezone: 'Asia/Singapore',
    geolocation: locations.singapore,
    networkCondition: networkConditions.wifi,
  },

  // 慢网络环境（中国）
  chinaSlow: {
    locale: 'zh-CN',
    timezone: 'Asia/Shanghai',
    geolocation: locations.beijing,
    networkCondition: networkConditions.slow3G,
  },

  // 移动网络环境（中国）
  chinaMobile: {
    locale: 'zh-CN',
    timezone: 'Asia/Shanghai',
    geolocation: locations.beijing,
    networkCondition: networkConditions.fourG,
  },
};

// 默认环境配置
export const defaultEnvironment: EnvironmentConfig = environments.china;

// 获取环境配置的辅助函数
export function getEnvironment(name: keyof typeof environments): EnvironmentConfig | undefined {
  return environments[name];
}

// 创建自定义环境配置
export function createCustomEnvironment(
  locale: string,
  timezone: string,
  options: {
    geolocation?: GeolocationConfig;
    networkCondition?: NetworkCondition;
  } = {}
): EnvironmentConfig {
  return {
    locale,
    timezone,
    geolocation: options.geolocation,
    networkCondition: options.networkCondition,
  };
}

// 创建自定义网络条件
export function createNetworkConditions(
  downloadThroughput: number,
  uploadThroughput: number,
  latency: number,
  offline: boolean = false
): NetworkCondition {
  return {
    offline,
    downloadThroughput,
    uploadThroughput,
    latency,
  };
}

// 获取网络条件配置
export function getNetworkConditions(name: string): NetworkCondition | undefined {
  return networkConditions[name];
}

// 获取地理位置配置
export function getLocation(name: string): GeolocationConfig | undefined {
  return locations[name];
}

// 支持的时区列表
export const supportedTimezones = [
  'Asia/Shanghai',
  'Asia/Tokyo',
  'Asia/Seoul',
  'Asia/Singapore',
  'Asia/Hong_Kong',
  'America/New_York',
  'America/Los_Angeles',
  'America/Chicago',
  'Europe/London',
  'Europe/Paris',
  'Europe/Berlin',
  'Australia/Sydney',
];

// 支持的语言区域列表
export const supportedLocales = [
  'zh-CN',
  'zh-TW',
  'zh-HK',
  'en-US',
  'en-GB',
  'en-AU',
  'ja-JP',
  'ko-KR',
  'fr-FR',
  'de-DE',
  'es-ES',
  'it-IT',
  'pt-BR',
  'ru-RU',
];

// 验证时区是否支持
export function isValidTimezone(timezone: string): boolean {
  return supportedTimezones.includes(timezone);
}

// 验证语言区域是否支持
export function isValidLocale(locale: string): boolean {
  return supportedLocales.includes(locale);
}

// 根据地理位置推荐时区
export function getRecommendedTimezone(geolocation: GeolocationConfig): string {
  const { longitude } = geolocation;

  // 简单的时区推荐逻辑，基于经度
  if (longitude >= 100 && longitude <= 140) {
    return 'Asia/Shanghai'; // 中国/东亚
  } else if (longitude >= 120 && longitude <= 150) {
    return 'Asia/Tokyo'; // 日本
  } else if (longitude >= -130 && longitude <= -60) {
    return 'America/New_York'; // 美国东部
  } else if (longitude >= -10 && longitude <= 30) {
    return 'Europe/London'; // 欧洲
  }

  return 'Asia/Shanghai'; // 默认时区
}

// 根据地理位置推荐语言区域
export function getRecommendedLocale(geolocation: GeolocationConfig): string {
  const { latitude, longitude } = geolocation;

  // 简单的语言区域推荐逻辑
  if (longitude >= 100 && longitude <= 140 && latitude >= 20 && latitude <= 50) {
    return 'zh-CN'; // 中国
  } else if (longitude >= 120 && longitude <= 150 && latitude >= 30 && latitude <= 45) {
    return 'ja-JP'; // 日本
  } else if (longitude >= -130 && longitude <= -60) {
    return 'en-US'; // 美国
  } else if (longitude >= -10 && longitude <= 30 && latitude >= 35 && latitude <= 70) {
    return 'en-GB'; // 欧洲（英国）
  }

  return 'zh-CN'; // 默认语言区域
}
