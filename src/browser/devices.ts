/**
 * 设备配置定义
 */

import type { DeviceConfig, DeviceName } from '../core/types';

// iOS Safari 设备配置
export const iOSDevices: Partial<Record<DeviceName, DeviceConfig>> = {
  iPhone13: {
    userAgent:
      'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
    viewport: { width: 390, height: 844 },
    deviceScaleFactor: 3.0,
    isMobile: true,
    hasTouch: true,
    defaultBrowserType: 'webkit',
  },
  iPhone14: {
    userAgent:
      'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
    viewport: { width: 393, height: 852 },
    deviceScaleFactor: 3.0,
    isMobile: true,
    hasTouch: true,
    defaultBrowserType: 'webkit',
  },
  iPhone15: {
    userAgent:
      'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
    viewport: { width: 393, height: 852 },
    deviceScaleFactor: 3.0,
    isMobile: true,
    hasTouch: true,
    defaultBrowserType: 'webkit',
  },
  iPad: {
    userAgent:
      'Mozilla/5.0 (iPad; CPU OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
    viewport: { width: 820, height: 1180 },
    deviceScaleFactor: 2.0,
    isMobile: false,
    hasTouch: true,
    defaultBrowserType: 'webkit',
  },
};

// Android Chrome 设备配置
export const androidDevices: Partial<Record<DeviceName, DeviceConfig>> = {
  GalaxyS21: {
    userAgent:
      'Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
    viewport: { width: 360, height: 800 },
    deviceScaleFactor: 3.0,
    isMobile: true,
    hasTouch: true,
    defaultBrowserType: 'chromium',
  },
  Pixel7: {
    userAgent:
      'Mozilla/5.0 (Linux; Android 13; Pixel 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
    viewport: { width: 412, height: 892 },
    deviceScaleFactor: 2.625,
    isMobile: true,
    hasTouch: true,
    defaultBrowserType: 'chromium',
  },
  Xiaomi13: {
    userAgent:
      'Mozilla/5.0 (Linux; Android 13; 2211133C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
    viewport: { width: 412, height: 915 },
    deviceScaleFactor: 2.75,
    isMobile: true,
    hasTouch: true,
    defaultBrowserType: 'chromium',
  },
};

// 桌面端浏览器配置
export const desktopDevices: Record<string, DeviceConfig> = {
  Chrome: {
    userAgent:
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    viewport: { width: 1920, height: 1080 },
    deviceScaleFactor: 1.0,
    isMobile: false,
    hasTouch: false,
    defaultBrowserType: 'chromium',
  },
  Firefox: {
    userAgent:
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/119.0',
    viewport: { width: 1920, height: 1080 },
    deviceScaleFactor: 1.0,
    isMobile: false,
    hasTouch: false,
    defaultBrowserType: 'firefox',
  },
  Safari: {
    userAgent:
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
    viewport: { width: 1920, height: 1080 },
    deviceScaleFactor: 1.0,
    isMobile: false,
    hasTouch: false,
    defaultBrowserType: 'webkit',
  },
};

// 所有设备配置的合集
export const allDevices: Partial<Record<DeviceName, DeviceConfig>> = {
  ...iOSDevices,
  ...androidDevices,
  ...desktopDevices,
};

// 预定义的设备组
export const deviceGroups = {
  mobile: { ...iOSDevices, ...androidDevices },
  desktop: desktopDevices,
  ios: iOSDevices,
  android: androidDevices,
};

// 获取设备配置的辅助函数
export function getDeviceConfig(deviceName: DeviceName): DeviceConfig | undefined {
  return allDevices[deviceName];
}

// 获取设备组配置
export function getDeviceGroup(groupName: keyof typeof deviceGroups): Record<string, DeviceConfig> {
  return deviceGroups[groupName] || {};
}

// 创建自定义设备配置
export function createCustomDevice(
  userAgent: string,
  viewport: { width: number; height: number },
  options: Partial<Omit<DeviceConfig, 'userAgent' | 'viewport'>> = {}
): DeviceConfig {
  return {
    userAgent,
    viewport,
    deviceScaleFactor: options.deviceScaleFactor || 1.0,
    isMobile: options.isMobile || false,
    hasTouch: options.hasTouch || false,
    defaultBrowserType: options.defaultBrowserType || 'chromium',
  };
}

// 常用的 User-Agent 池
export const userAgentPool = {
  ios: [
    'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
    'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
    'Mozilla/5.0 (iPad; CPU OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
  ],
  android: [
    'Mozilla/5.0 (Linux; Android 13; Pixel 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
    'Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
    'Mozilla/5.0 (Linux; Android 12; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
  ],
  desktop: [
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/119.0',
  ],
} as const;

// 随机获取 User-Agent
export function getRandomUserAgent(platform: keyof typeof userAgentPool): string {
  const agents = userAgentPool[platform];

  const randomIndex = Math.floor(Math.random() * agents.length);

  return agents[randomIndex]!;
}
