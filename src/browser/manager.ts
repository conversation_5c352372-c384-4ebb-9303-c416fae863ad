/**
 * 浏览器实例管理器
 */

import {
  chromium,
  firefox,
  webkit,
  type Browser,
  type BrowserContext,
  type Page,
} from 'playwright';
import type { DeviceConfig, EnvironmentConfig, IBrowserManager, RedirectType } from '../types';
import { allDevices } from './devices';
import { defaultEnvironment } from './environment';

export class BrowserManager implements IBrowserManager {
  private browsers: Map<string, Browser> = new Map();
  private contexts: Set<BrowserContext> = new Set();

  /**
   * 获取或创建浏览器实例
   */
  private async getBrowser(browserType: 'chromium' | 'firefox' | 'webkit'): Promise<Browser> {
    if (this.browsers.has(browserType)) {
      return this.browsers.get(browserType)!;
    }

    let browser: Browser;
    const launchOptions = {
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
      ],
    };

    switch (browserType) {
      case 'chromium':
        browser = await chromium.launch(launchOptions);
        break;
      case 'firefox':
        browser = await firefox.launch(launchOptions);
        break;
      case 'webkit':
        browser = await webkit.launch(launchOptions);
        break;
      default:
        throw new Error(`Unsupported browser type: ${browserType}`);
    }

    this.browsers.set(browserType, browser);
    return browser;
  }

  /**
   * 创建浏览器上下文
   */
  async createContext(
    deviceConfig?: DeviceConfig,
    environment?: EnvironmentConfig
  ): Promise<BrowserContext> {
    // 使用默认配置
    const device = deviceConfig || allDevices.iPhone15;
    const env = environment || defaultEnvironment;

    // 获取浏览器实例
    const browser = await this.getBrowser(device!.defaultBrowserType);

    // 创建上下文配置
    const contextOptions: any = {
      userAgent: device!.userAgent,
      viewport: device!.viewport,
      deviceScaleFactor: device!.deviceScaleFactor,
      isMobile: device!.isMobile,
      hasTouch: device!.hasTouch,
      locale: env.locale,
      timezoneId: env.timezone,
      permissions: ['geolocation'],
    };

    // 设置地理位置
    if (env.geolocation) {
      contextOptions.geolocation = env.geolocation;
    }

    // 创建上下文
    const context = await browser.newContext(contextOptions);

    // 设置网络条件
    if (env.networkCondition) {
      const page = await context.newPage();
      const client = await page.context().newCDPSession(page);

      if (env.networkCondition.offline) {
        await client.send('Network.emulateNetworkConditions', {
          offline: true,
          downloadThroughput: 0,
          uploadThroughput: 0,
          latency: 0,
        });
      } else {
        await client.send('Network.emulateNetworkConditions', {
          offline: false,
          downloadThroughput: env.networkCondition.downloadThroughput || 0,
          uploadThroughput: env.networkCondition.uploadThroughput || 0,
          latency: env.networkCondition.latency || 0,
        });
      }

      await page.close();
    }

    this.contexts.add(context);
    return context;
  }

  /**
   * 销毁浏览器上下文
   */
  async destroyContext(context: BrowserContext): Promise<void> {
    if (this.contexts.has(context)) {
      await context.close();
      this.contexts.delete(context);
    }
  }

  /**
   * 创建页面并设置基础配置
   */
  async createPage(context: BrowserContext): Promise<Page> {
    const page = await context.newPage();

    // 设置默认超时
    page.setDefaultTimeout(30000);
    page.setDefaultNavigationTimeout(30000);

    return page;
  }

  /**
   * 注入跳转监听脚本
   */
  async injectRedirectListeners(page: Page): Promise<void> {
    await page.addInitScript(() => {
      // 创建全局跳转检测对象
      // @ts-ignore
      window.__redirectDetector = {
        redirects: [],
        startTime: Date.now(),

        // 记录跳转信息
        recordRedirect: function (type: string, targetUrl: string, sourceCode?: string) {
          this.redirects.push({
            type: type as RedirectType,
            targetUrl,
            triggerTime: Date.now() - this.startTime,
            sourceCode,
            timestamp: Date.now(),
            stack: new Error().stack,
          });
        },
      };

      // @ts-ignore
      const detector = window.__redirectDetector;

      // 拦截 location 对象操作
      // @ts-ignore
      const originalLocation = window.location;
      const locationProxy = new Proxy(originalLocation, {
        set(target, property, value) {
          if (
            property === 'href' ||
            property === 'pathname' ||
            property === 'search' ||
            property === 'hash'
          ) {
            detector?.recordRedirect(
              'location',
              value.toString(),
              `location.${property.toString()} = "${value}"`
            );
          }
          return Reflect.set(target, property, value);
        },
      });

      // 替换 location 对象
      // @ts-ignore
      Object.defineProperty(window, 'location', {
        get: () => locationProxy,
        set: (value) => {
          detector?.recordRedirect('location', value.toString(), `location = "${value}"`);
          originalLocation.href = value;
        },
      });

      // 拦截 location 方法
      const originalReplace = originalLocation.replace;
      const originalAssign = originalLocation.assign;

      originalLocation.replace = function (url: string) {
        detector?.recordRedirect('location', url, `location.replace("${url}")`);
        return originalReplace.call(this, url);
      };

      originalLocation.assign = function (url: string) {
        detector?.recordRedirect('location', url, `location.assign("${url}")`);
        return originalAssign.call(this, url);
      };

      // 拦截 window.open
      // @ts-ignore
      const originalOpen = window.open;
      // @ts-ignore
      window.open = function (url?: string, target?: string, features?: string) {
        if (target === '_self' || !target) {
          detector?.recordRedirect('location', url || '', `window.open("${url}", "${target}")`);
        }
        return originalOpen.call(this, url, target, features);
      };

      // 拦截定时器
      // @ts-ignore
      const originalSetTimeout = window.setTimeout;
      // @ts-ignore
      const originalSetInterval = window.setInterval;

      // @ts-ignore
      window.setTimeout = function (callback: any, delay?: number, ...args: any[]) {
        if (typeof callback === 'string') {
          // 检查字符串中是否包含跳转代码
          if (/location\.|window\.open|href\s*=/.test(callback)) {
            detector?.recordRedirect('timer', 'unknown', `setTimeout("${callback}", ${delay})`);
          }
        } else if (typeof callback === 'function') {
          const wrappedCallback = function () {
            // @ts-ignore
            const result = callback.apply(this, arguments);
            // 检查是否在回调中触发了跳转
            return result;
          };
          return originalSetTimeout.call(this, wrappedCallback, delay, ...(args as []));
        }
        return originalSetTimeout.call(this, callback, delay, ...(args as []));
      };

      // @ts-ignore
      window.setInterval = function (callback: any, delay?: number, ...args: any[]) {
        if (typeof callback === 'string') {
          if (/location\.|window\.open|href\s*=/.test(callback)) {
            detector?.recordRedirect('timer', 'unknown', `setInterval("${callback}", ${delay})`);
          }
        }
        return originalSetInterval.call(this, callback, delay, ...(args as []));
      };

      // 监听表单提交
      // @ts-ignore
      document.addEventListener(
        'submit',
        function (event: any) {
          const form = event.target as HTMLFormElement;
          if (form && form.action) {
            detector?.recordRedirect('form', form.action, `form.submit() to "${form.action}"`);
          }
        },
        true
      );

      // 监听点击事件（检测模拟点击）
      // @ts-ignore
      document.addEventListener(
        'click',
        function (event) {
          const target = event.target as HTMLElement;
          if (target && target.tagName === 'A') {
            const href = (target as HTMLAnchorElement).href;
            if (href && !event.isTrusted) {
              detector?.recordRedirect(
                'simulated_click',
                href,
                `Simulated click on <a href="${href}">`
              );
            }
          }
        },
        true
      );

      // 检测 meta refresh
      const observer = new MutationObserver(function (mutations) {
        mutations.forEach(function (mutation) {
          mutation.addedNodes.forEach(function (node) {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              if (element.tagName === 'META' && element.getAttribute('http-equiv') === 'refresh') {
                const content = element.getAttribute('content');
                if (content) {
                  const match = content.match(/url=(.+)/i);
                  const url = match ? match[1] : 'unknown';
                  detector?.recordRedirect(
                    'meta',
                    url || '',
                    `<meta http-equiv="refresh" content="${content}">`
                  );
                }
              }
            }
          });
        });
      });

      observer.observe(document.documentElement, {
        childList: true,
        subtree: true,
      });
    });
  }

  /**
   * 获取检测到的跳转信息
   */
  async getRedirectInfo(page: Page): Promise<any[]> {
    return await page.evaluate(() => {
      return (window as any).__redirectDetector?.redirects || [];
    });
  }

  /**
   * 清理所有资源
   */
  async cleanup(): Promise<void> {
    try {
      // 关闭所有上下文
      const contextPromises = Array.from(this.contexts).map(async (context) => {
        try {
          await context.close();
        } catch (error) {
          console.warn('Failed to close context:', error);
        }
      });
      await Promise.all(contextPromises);
      this.contexts.clear();

      // 关闭所有浏览器
      const browserPromises = Array.from(this.browsers.entries()).map(async ([type, browser]) => {
        try {
          await browser.close();
        } catch (error) {
          console.warn(`Failed to close ${type} browser:`, error);
        }
      });
      await Promise.all(browserPromises);
      this.browsers.clear();
    } catch (error) {
      console.error('Error during browser cleanup:', error);
    }
  }
}
