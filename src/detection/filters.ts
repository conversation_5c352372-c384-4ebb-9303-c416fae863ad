/**
 * 资源过滤器
 */

import type { Page, Request } from 'playwright';
import type { ResourceFilterConfig } from '../core/types';

export class ResourceFilter {
  private config: ResourceFilterConfig;
  private currentDomain: string = '';

  constructor(config?: Partial<ResourceFilterConfig>) {
    this.config = {
      interceptResourceTypes: ['document', 'stylesheet', 'script', 'xhr', 'fetch', 'other'],
      allowedResourceTypes: ['image', 'media', 'font', 'websocket'],
      domainWhitelist: [],
      blockThirdParty: false,
      ...config,
    };
  }

  /**
   * 设置页面资源过滤
   */
  async setupResourceFiltering(page: Page, baseUrl: string): Promise<void> {
    // 提取当前域名
    this.currentDomain = this.extractDomain(baseUrl);

    // 设置请求拦截
    await page.route('**/*', (route, request) => {
      if (this.shouldInterceptRequest(request)) {
        // 记录请求信息
        this.logRequest(request);

        // 继续请求
        route.continue();
      } else {
        // 允许请求通过
        route.continue();
      }
    });
  }

  /**
   * 判断是否应该拦截请求
   */
  private shouldInterceptRequest(request: Request): boolean {
    const url = request.url();
    const resourceType = request.resourceType();
    const domain = this.extractDomain(url);

    // 检查资源类型是否在拦截列表中
    if (!this.config.interceptResourceTypes.includes(resourceType)) {
      return false;
    }

    // 检查资源类型是否在允许列表中
    if (this.config.allowedResourceTypes.includes(resourceType)) {
      return false;
    }

    // 检查是否为第三方域名
    if (this.config.blockThirdParty && !this.isSameDomain(domain, this.currentDomain)) {
      // 检查是否在白名单中
      if (!this.isInWhitelist(domain)) {
        return false;
      }
    }

    return true;
  }

  /**
   * 提取域名
   */
  private extractDomain(url: string): string {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.toLowerCase();
    } catch (error) {
      return '';
    }
  }

  /**
   * 检查是否为同一域名或子域名
   */
  private isSameDomain(domain1: string, domain2: string): boolean {
    if (domain1 === domain2) {
      return true;
    }

    // 检查子域名
    const parts1 = domain1.split('.').reverse();
    const parts2 = domain2.split('.').reverse();

    const minLength = Math.min(parts1.length, parts2.length);

    for (let i = 0; i < minLength; i++) {
      if (parts1[i] !== parts2[i]) {
        return false;
      }
    }

    return true;
  }

  /**
   * 检查域名是否在白名单中
   */
  private isInWhitelist(domain: string): boolean {
    return this.config.domainWhitelist.some((whitelistDomain) => {
      // 支持通配符匹配
      if (whitelistDomain.startsWith('*.')) {
        const baseDomain = whitelistDomain.substring(2);
        return domain.endsWith(baseDomain);
      }

      // 支持正则表达式匹配
      if (whitelistDomain.startsWith('/') && whitelistDomain.endsWith('/')) {
        const regex = new RegExp(whitelistDomain.slice(1, -1));
        return regex.test(domain);
      }

      // 精确匹配
      return domain === whitelistDomain;
    });
  }

  /**
   * 记录请求信息
   */
  private logRequest(request: Request): void {
    // 这里可以记录请求信息，用于后续分析
    // console.log(`Intercepted request: ${request.method()} ${request.url()}`);
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<ResourceFilterConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 获取当前配置
   */
  getConfig(): ResourceFilterConfig {
    return { ...this.config };
  }

  /**
   * 添加域名到白名单
   */
  addToWhitelist(domain: string): void {
    if (!this.config.domainWhitelist.includes(domain)) {
      this.config.domainWhitelist.push(domain);
    }
  }

  /**
   * 从白名单中移除域名
   */
  removeFromWhitelist(domain: string): void {
    const index = this.config.domainWhitelist.indexOf(domain);
    if (index > -1) {
      this.config.domainWhitelist.splice(index, 1);
    }
  }

  /**
   * 添加拦截的资源类型
   */
  addInterceptResourceType(resourceType: string): void {
    if (!this.config.interceptResourceTypes.includes(resourceType)) {
      this.config.interceptResourceTypes.push(resourceType);
    }
  }

  /**
   * 移除拦截的资源类型
   */
  removeInterceptResourceType(resourceType: string): void {
    const index = this.config.interceptResourceTypes.indexOf(resourceType);
    if (index > -1) {
      this.config.interceptResourceTypes.splice(index, 1);
    }
  }

  /**
   * 添加允许的资源类型
   */
  addAllowedResourceType(resourceType: string): void {
    if (!this.config.allowedResourceTypes.includes(resourceType)) {
      this.config.allowedResourceTypes.push(resourceType);
    }
  }

  /**
   * 移除允许的资源类型
   */
  removeAllowedResourceType(resourceType: string): void {
    const index = this.config.allowedResourceTypes.indexOf(resourceType);
    if (index > -1) {
      this.config.allowedResourceTypes.splice(index, 1);
    }
  }

  /**
   * 重置配置为默认值
   */
  resetConfig(): void {
    this.config = {
      interceptResourceTypes: ['document', 'stylesheet', 'script', 'xhr', 'fetch', 'other'],
      allowedResourceTypes: ['image', 'media', 'font', 'websocket'],
      domainWhitelist: [],
      blockThirdParty: false,
    };
  }

  /**
   * 获取预设的常见CDN和公共服务域名白名单
   */
  static getCommonCDNWhitelist(): string[] {
    return [
      // 常见CDN
      '*.cloudflare.com',
      '*.amazonaws.com',
      '*.cloudfront.net',
      '*.fastly.com',
      '*delivr.net',
      '*.unpkg.com',
      '*.cdnjs.cloudflare.com',

      // 中国CDN
      '*.alicdn.com',
      '*.qcloud.com',
      '*.myqcloud.com',
      '*.netdna-cdn.com',
      '*.bootcdn.net',

      // 字体服务
      'fonts.googleapis.com',
      'fonts.gstatic.com',

      // 分析服务
      'www.google-analytics.com',
      'www.googletagmanager.com',

      // 社交媒体
      'connect.facebook.net',
      'platform.twitter.com',
    ];
  }

  /**
   * 应用常见CDN白名单
   */
  applyCommonCDNWhitelist(): void {
    const commonCDNs = ResourceFilter.getCommonCDNWhitelist();
    this.config.domainWhitelist = [
      ...this.config.domainWhitelist,
      ...commonCDNs.filter((cdn) => !this.config.domainWhitelist.includes(cdn)),
    ];
  }
}
