/**
 * 结果分析器
 */

import type {
  DetectionResult,
  DetectionStatus,
  DeviceInfo,
  EnvironmentConfig,
  ErrorInfo,
  NetworkRequest,
  RedirectEvent,
  RedirectInfo,
  Screenshots,
  Timestamps,
} from '../types';

export class ResultAnalyzer {
  /**
   * 分析检测结果
   */
  static analyzeDetectionResult(
    status: DetectionStatus,
    redirectEvents: RedirectEvent[],
    startTime: number,
    endTime: number,
    deviceInfo: DeviceInfo,
    environment?: EnvironmentConfig,
    errors?: ErrorInfo[],
    screenshots?: Screenshots,
    networkRequests?: NetworkRequest[],
    pageLoadTime?: number
  ): DetectionResult {
    const timestamps: Timestamps = {
      startTime,
      endTime,
      pageLoadTime,
      totalTime: endTime - startTime,
    };

    const redirectInfo = this.analyzeRedirectEvents(redirectEvents);

    return {
      status,
      redirectInfo,
      redirectEvents: redirectEvents || [],
      timestamps,
      deviceInfo,
      environment,
      errors,
      screenshots,
      networkRequests,
    };
  }

  /**
   * 分析跳转事件
   */
  private static analyzeRedirectEvents(events: RedirectEvent[]): RedirectInfo | undefined {
    if (events.length === 0) {
      return { hasRedirect: false };
    }

    // 按触发时间排序
    const sortedEvents = events.sort((a, b) => a.triggerTime - b.triggerTime);

    // 分析跳转类型优先级
    const prioritizedEvent = this.getPrioritizedRedirect(sortedEvents);

    if (!prioritizedEvent) {
      return { hasRedirect: false };
    }

    return {
      hasRedirect: true,
      redirectType: prioritizedEvent.type,
      targetUrl: prioritizedEvent.targetUrl,
      triggerTime: prioritizedEvent.triggerTime,
      redirectDelay: prioritizedEvent.triggerTime,
      sourceCode: prioritizedEvent.sourceCode,
    };
  }

  /**
   * 根据优先级获取最重要的跳转事件
   */
  private static getPrioritizedRedirect(events: RedirectEvent[]): RedirectEvent | null {
    if (events.length === 0) {
      return null;
    }
    // 跳转类型优先级（按重要性排序）
    const typePriority: Record<string, number> = {
      meta: 1, // meta refresh 优先级最高
      timer: 2, // 定时器跳转
      location: 3, // location 跳转
      form: 4, // 表单提交
      simulated_click: 5, // 模拟点击优先级最低
    };

    // 按优先级和触发时间排序
    const sortedEvents = events.sort((a, b) => {
      const priorityA = typePriority[a.type] || 999;
      const priorityB = typePriority[b.type] || 999;

      if (priorityA !== priorityB) {
        return priorityA - priorityB;
      }

      // 优先级相同时，按触发时间排序
      return a.triggerTime - b.triggerTime;
    });

    return sortedEvents[0] || null;
  }
}
