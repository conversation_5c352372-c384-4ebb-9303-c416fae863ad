/**
 * 任务执行器
 */

import type { BrowserContext, Page } from 'playwright';
import { BrowserManager } from '../browser/manager';
import type {
  DetectionOptions,
  DetectionResult,
  DeviceConfig,
  ErrorInfo,
  TimeoutConfig,
} from '../core/types';
import { ResultAnalyzer } from '../detection/analyzer';
import { ResourceFilter } from '../detection/filters';
import { RedirectListener } from '../detection/listeners';
import { QueueManager, type QueueTask } from './queue-manager';

export interface TaskExecutionContext {
  browserManager: BrowserManager;
  queueManager: QueueManager;
  baseUrl: string;
}

export class TaskExecutor {
  private context: TaskExecutionContext;
  private timeoutConfig: TimeoutConfig;

  constructor(context: TaskExecutionContext, timeoutConfig?: Partial<TimeoutConfig>) {
    this.context = context;
    this.timeoutConfig = {
      pageLoad: 10000,
      redirectDetection: 6000,
      networkIdle: 2000,
      totalTask: 15000,
      timerRedirect: 8000,
      metaRefresh: 3000,
      formSubmit: 5000,
      ...timeoutConfig,
    };
  }

  /**
   * 执行单个检测任务
   */
  async executeDetectionTask(options: DetectionOptions): Promise<DetectionResult> {
    const startTime = Date.now();
    let context: BrowserContext | null = null;
    let page: Page | null = null;
    const errors: ErrorInfo[] = [];

    try {
      // 创建浏览器上下文
      context = await this.context.browserManager.createContext(
        options.deviceConfig,
        options.environment
      );

      // 创建页面
      page = await this.context.browserManager.createPage(context);

      // 设置超时
      page.setDefaultTimeout(this.timeoutConfig.pageLoad);
      page.setDefaultNavigationTimeout(this.timeoutConfig.pageLoad);

      // 创建跳转监听器
      const redirectListener = new RedirectListener();
      await redirectListener.startListening(page);

      // 设置资源过滤
      const resourceFilter = new ResourceFilter(options.config?.resourceFilter);

      // 确定要访问的URL
      let targetUrl: string;
      if (options.url) {
        targetUrl = options.url;
      } else if (options.htmlFile) {
        const relativePath = options.htmlFile.replace(process.cwd(), '').replace(/^\/+/, '');
        targetUrl = `${this.context.baseUrl}/${relativePath}`;
      } else if (options.htmlContent) {
        // 这种情况需要先创建临时文件
        throw new Error('HTML content detection requires file manager integration');
      } else {
        throw new Error('No URL, HTML file, or HTML content provided');
      }

      await resourceFilter.setupResourceFiltering(page, targetUrl);

      // 记录页面加载开始时间
      const pageLoadStart = Date.now();

      // 导航到目标页面
      await page.goto(targetUrl, {
        waitUntil: 'networkidle',
        timeout: this.timeoutConfig.pageLoad,
      });

      const pageLoadTime = Date.now() - pageLoadStart;

      // 等待跳转检测
      await this.waitForRedirectDetection(page, redirectListener);

      // 从页面中获取跳转事件（优先使用页面事件，因为包含正确的类型信息）
      let pageRedirectEvents = [];
      try {
        pageRedirectEvents = await page.evaluate(() => {
          return (window as any).__redirectDetector?.redirects || [];
        });
        console.log('Page redirect events:', pageRedirectEvents);
      } catch (error) {
        console.warn('Failed to get redirect events from page:', error);
      }

      // 获取监听器事件作为备用
      const listenerEvents = redirectListener.getRedirectEvents();
      console.log('Listener events:', listenerEvents);

      // 优先使用页面事件，如果没有则使用监听器事件
      let redirectEvents = pageRedirectEvents.length > 0 ? pageRedirectEvents : listenerEvents;

      // 如果两者都有，合并并去重
      if (pageRedirectEvents.length > 0 && listenerEvents.length > 0) {
        const allEvents = [...pageRedirectEvents, ...listenerEvents];
        redirectEvents = allEvents.filter((event, index, arr) =>
          arr.findIndex(e => e.targetUrl === event.targetUrl) === index
        ).sort((a, b) => (a.triggerTime || 0) - (b.triggerTime || 0));
      }

      // 停止监听
      redirectListener.stopListening();

      // 获取设备信息
      const deviceInfo = this.getDeviceInfo(options.deviceConfig);

      // 生成检测结果
      const endTime = Date.now();
      const result = ResultAnalyzer.analyzeDetectionResult(
        'success',
        redirectEvents,
        startTime,
        endTime,
        deviceInfo,
        options.environment,
        errors.length > 0 ? errors : undefined,
        undefined, // screenshots
        undefined, // networkRequests
        pageLoadTime
      );

      return result;
    } catch (error) {
      const errorInfo: ErrorInfo = {
        type: this.categorizeError(error as Error),
        message: (error as Error).message,
        stack: (error as Error).stack,
        timestamp: Date.now(),
      };
      errors.push(errorInfo);

      const endTime = Date.now();
      const deviceInfo = this.getDeviceInfo(options.deviceConfig);

      return ResultAnalyzer.analyzeDetectionResult(
        'failed',
        [],
        startTime,
        endTime,
        deviceInfo,
        options.environment,
        errors
      );
    } finally {
      // 清理资源
      if (page) {
        try {
          await page.close();
        } catch (error) {
          console.warn('Failed to close page:', error);
        }
      }

      if (context) {
        try {
          await this.context.browserManager.destroyContext(context);
        } catch (error) {
          console.warn('Failed to destroy context:', error);
        }
      }
    }
  }

  /**
   * 等待跳转检测完成
   */
  private async waitForRedirectDetection(
    page: Page,
    redirectListener: RedirectListener
  ): Promise<void> {
    const detectionTimeout = this.timeoutConfig.redirectDetection;
    const networkIdleTimeout = this.timeoutConfig.networkIdle;

    return new Promise<void>((resolve) => {
      let resolved = false;

      // 设置检测超时
      const detectionTimer = setTimeout(() => {
        if (!resolved) {
          resolved = true;
          resolve();
        }
      }, detectionTimeout);

      // 监听跳转事件
      const checkRedirects = () => {
        const events = redirectListener.getRedirectEvents();
        if (events.length > 0 && !resolved) {
          // 检测到跳转，等待网络空闲后结束
          setTimeout(() => {
            if (!resolved) {
              resolved = true;
              clearTimeout(detectionTimer);
              resolve();
            }
          }, networkIdleTimeout);
        }
      };

      // 定期检查跳转事件
      const checkInterval = setInterval(() => {
        if (resolved) {
          clearInterval(checkInterval);
          return;
        }
        checkRedirects();
      }, 100);

      // 清理定时器
      setTimeout(() => {
        clearInterval(checkInterval);
        clearTimeout(detectionTimer);
      }, detectionTimeout + 1000);
    });
  }

  /**
   * 获取设备信息
   */
  private getDeviceInfo(deviceConfig?: DeviceConfig): any {
    // 这里应该从实际的设备配置中获取信息
    // 暂时返回默认值
    return {
      type: 'mobile',
      browser: 'chrome',
      userAgent: deviceConfig?.userAgent || 'default',
      viewport: deviceConfig?.viewport || { width: 375, height: 667 },
      deviceScaleFactor: deviceConfig?.deviceScaleFactor || 1.0,
    };
  }

  /**
   * 错误分类
   */
  private categorizeError(error: Error): any {
    const message = error.message.toLowerCase();

    if (message.includes('timeout')) {
      return 'TIMEOUT_ERROR';
    } else if (message.includes('network') || message.includes('connection')) {
      return 'NETWORK_ERROR';
    } else if (message.includes('parse') || message.includes('invalid')) {
      return 'PARSE_ERROR';
    } else if (message.includes('browser') || message.includes('page')) {
      return 'BROWSER_ERROR';
    } else if (message.includes('validation')) {
      return 'VALIDATION_ERROR';
    } else {
      return 'DETECTION_ERROR';
    }
  }

  /**
   * 创建检测任务
   */
  createDetectionTask(
    taskName: string,
    options: DetectionOptions,
    taskOptions: {
      priority?: number;
      timeout?: number;
      retryCount?: number;
    } = {}
  ): QueueTask<DetectionResult> {
    return QueueManager.createTask(taskName, () => this.executeDetectionTask(options), {
      timeout: taskOptions.timeout || this.timeoutConfig.totalTask,
      priority: taskOptions.priority,
      retryCount: taskOptions.retryCount,
    });
  }

  /**
   * 批量创建检测任务
   */
  createBatchDetectionTasks(
    optionsList: DetectionOptions[],
    taskOptions: {
      priority?: number;
      timeout?: number;
      retryCount?: number;
    } = {}
  ): QueueTask<DetectionResult>[] {
    return optionsList.map((options, index) =>
      this.createDetectionTask(`detection_task_${index}`, options, taskOptions)
    );
  }

  /**
   * 执行批量检测任务
   */
  async executeBatchDetection(optionsList: DetectionOptions[]): Promise<DetectionResult[]> {
    const tasks = this.createBatchDetectionTasks(optionsList);
    const taskIds = await this.context.queueManager.addTasks(tasks);

    // 等待所有任务完成
    await this.context.queueManager.waitForAll();

    // 收集结果
    const results: DetectionResult[] = [];
    for (const taskId of taskIds) {
      try {
        const result = this.context.queueManager.getTaskResult<DetectionResult>(taskId);
        if (result) {
          results.push(result);
        }
      } catch (error) {
        // 任务失败，创建失败结果
        const failedResult: DetectionResult = {
          status: 'failed',
          timestamps: {
            startTime: Date.now(),
            endTime: Date.now(),
            totalTime: 0,
          },
          deviceInfo: this.getDeviceInfo(),
          errors: [
            {
              type: 'DETECTION_ERROR',
              message: (error as Error).message,
              timestamp: Date.now(),
            },
          ],
        };
        results.push(failedResult);
      }
    }

    return results;
  }

  /**
   * 更新超时配置
   */
  updateTimeoutConfig(newConfig: Partial<TimeoutConfig>): void {
    this.timeoutConfig = { ...this.timeoutConfig, ...newConfig };
  }

  /**
   * 获取当前超时配置
   */
  getTimeoutConfig(): TimeoutConfig {
    return { ...this.timeoutConfig };
  }
}
