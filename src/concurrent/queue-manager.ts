/**
 * 队列管理器
 */

import PQueue from 'p-queue';
import type { ConcurrentConfig } from '../core/types';

export interface QueueTask<T = any> {
  id: string;
  name: string;
  execute: () => Promise<T>;
  priority?: number;
  timeout?: number;
  retryCount?: number;
}

export interface QueueStats {
  pending: number;
  running: number;
  completed: number;
  failed: number;
  total: number;
}

export class QueueManager {
  private queue: PQueue;
  private config: ConcurrentConfig;
  private stats: QueueStats = {
    pending: 0,
    running: 0,
    completed: 0,
    failed: 0,
    total: 0,
  };
  private taskResults: Map<string, any> = new Map();
  private taskErrors: Map<string, Error> = new Map();

  constructor(config?: Partial<ConcurrentConfig>) {
    this.config = {
      maxConcurrent: config?.maxConcurrent || 5,
      queueSize: config?.queueSize || 100,
      taskTimeout: config?.taskTimeout || 60000,
      retryCount: config?.retryCount || 3,
      ...config,
    };

    this.queue = new PQueue({
      concurrency: this.config.maxConcurrent,
      timeout: this.config.taskTimeout,
      throwOnTimeout: true,
    });

    this.setupEventListeners();
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    this.queue.on('active', () => {
      this.stats.running = this.queue.pending;
      this.stats.pending = this.queue.size;
    });

    this.queue.on('idle', () => {
      this.stats.running = 0;
      this.stats.pending = 0;
    });

    this.queue.on('add', () => {
      this.stats.total++;
      this.stats.pending = this.queue.size;
    });
  }

  /**
   * 添加任务到队列
   */
  async addTask<T>(task: QueueTask<T>): Promise<string> {
    if (this.queue.size >= this.config.queueSize) {
      throw new Error(`Queue is full. Maximum size: ${this.config.queueSize}`);
    }

    const taskId = task.id;
    const priority = task.priority || 0;
    const timeout = task.timeout || this.config.taskTimeout;
    const maxRetries = task.retryCount || this.config.retryCount;

    // 包装任务执行函数，添加重试逻辑
    const wrappedExecute = async (): Promise<T> => {
      let lastError: Error | null = null;

      for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
          const result = await this.executeWithTimeout(task.execute, timeout);
          this.stats.completed++;
          this.taskResults.set(taskId, result);
          return result;
        } catch (error) {
          lastError = error as Error;

          if (attempt < maxRetries) {
            // 指数退避策略
            const delay = Math.min(1000 * Math.pow(2, attempt), 10000);
            await this.sleep(delay);
          }
        }
      }

      this.stats.failed++;
      this.taskErrors.set(taskId, lastError!);
      throw lastError;
    };

    // 添加到队列
    this.queue.add(wrappedExecute, { priority });

    return taskId;
  }

  /**
   * 批量添加任务
   */
  async addTasks<T>(tasks: QueueTask<T>[]): Promise<string[]> {
    const taskIds: string[] = [];

    for (const task of tasks) {
      const taskId = await this.addTask(task);
      taskIds.push(taskId);
    }

    return taskIds;
  }

  /**
   * 等待任务完成
   */
  async waitForTask<T>(taskId: string): Promise<T> {
    // 等待队列中的任务完成
    await this.queue.onIdle();

    if (this.taskResults.has(taskId)) {
      return this.taskResults.get(taskId);
    }

    if (this.taskErrors.has(taskId)) {
      throw this.taskErrors.get(taskId);
    }

    throw new Error(`Task ${taskId} not found`);
  }

  /**
   * 等待所有任务完成
   */
  async waitForAll(): Promise<void> {
    await this.queue.onIdle();
  }

  /**
   * 获取任务结果
   */
  getTaskResult<T>(taskId: string): T | undefined {
    return this.taskResults.get(taskId);
  }

  /**
   * 获取任务错误
   */
  getTaskError(taskId: string): Error | undefined {
    return this.taskErrors.get(taskId);
  }

  /**
   * 获取所有任务结果
   */
  getAllResults<T>(): Map<string, T> {
    return new Map(this.taskResults);
  }

  /**
   * 获取所有任务错误
   */
  getAllErrors(): Map<string, Error> {
    return new Map(this.taskErrors);
  }

  /**
   * 获取队列统计信息
   */
  getStats(): QueueStats {
    return {
      ...this.stats,
      pending: this.queue.size,
      running: this.queue.pending,
    };
  }

  /**
   * 清空队列
   */
  clear(): void {
    this.queue.clear();
    this.taskResults.clear();
    this.taskErrors.clear();
    this.stats = {
      pending: 0,
      running: 0,
      completed: 0,
      failed: 0,
      total: 0,
    };
  }

  /**
   * 暂停队列
   */
  pause(): void {
    this.queue.pause();
  }

  /**
   * 恢复队列
   */
  start(): void {
    this.queue.start();
  }

  /**
   * 检查队列是否暂停
   */
  isPaused(): boolean {
    return this.queue.isPaused;
  }

  /**
   * 获取队列大小
   */
  size(): number {
    return this.queue.size;
  }

  /**
   * 获取正在运行的任务数量
   */
  pending(): number {
    return this.queue.pending;
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<ConcurrentConfig>): void {
    this.config = { ...this.config, ...newConfig };

    // 更新队列配置
    if (newConfig.maxConcurrent !== undefined) {
      this.queue.concurrency = newConfig.maxConcurrent;
    }
  }

  /**
   * 获取当前配置
   */
  getConfig(): ConcurrentConfig {
    return { ...this.config };
  }

  /**
   * 带超时的任务执行
   */
  private async executeWithTimeout<T>(execute: () => Promise<T>, timeout: number): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`Task timeout after ${timeout}ms`));
      }, timeout);

      execute()
        .then((result) => {
          clearTimeout(timer);
          resolve(result);
        })
        .catch((error) => {
          clearTimeout(timer);
          reject(error);
        });
    });
  }

  /**
   * 睡眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * 销毁队列管理器
   */
  async destroy(): Promise<void> {
    // 等待所有任务完成或清空队列
    this.queue.clear();

    // 清理资源
    this.taskResults.clear();
    this.taskErrors.clear();
  }

  /**
   * 创建任务ID
   */
  static createTaskId(prefix: string = 'task'): string {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 创建简单任务
   */
  static createTask<T>(
    name: string,
    execute: () => Promise<T>,
    options: {
      id?: string;
      priority?: number;
      timeout?: number;
      retryCount?: number;
    } = {}
  ): QueueTask<T> {
    return {
      id: options.id || this.createTaskId(),
      name,
      execute,
      priority: options.priority,
      timeout: options.timeout,
      retryCount: options.retryCount,
    };
  }
}
