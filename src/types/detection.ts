/**
 * 检测相关的类型定义
 */

// 检测结果状态
export type DetectionStatus = 'success' | 'failed' | 'timeout';

// 跳转类型
export type RedirectType = 'timer' | 'location' | 'meta' | 'form' | 'simulated_click' | 'navigation';

// 错误类型
export type ErrorType =
  | 'NETWORK_ERROR'
  | 'TIMEOUT_ERROR'
  | 'PARSE_ERROR'
  | 'BROWSER_ERROR'
  | 'VALIDATION_ERROR'
  | 'DETECTION_ERROR'
  | 'DYNAMIC_CODE_ERROR';

// 跳转事件接口
export interface RedirectEvent {
  type: RedirectType;
  targetUrl: string;
  triggerTime: number;
  sourceCode?: string;
  timestamp: number;
  stack?: string;
}

// 跳转详情接口
export interface RedirectInfo {
  hasRedirect: boolean;
  redirectType?: RedirectType;
  targetUrl?: string;
  triggerTime?: number; // 跳转触发时间（毫秒）
  redirectDelay?: number; // 跳转延迟时间（毫秒）
  sourceCode?: string; // 触发跳转的源代码片段
}

// 错误信息接口
export interface ErrorInfo {
  type: ErrorType;
  message: string;
  stack?: string;
  timestamp: number;
}

// 时间戳信息
export interface Timestamps {
  startTime: number; // 检测开始时间戳
  endTime: number; // 检测结束时间戳
  totalTime: number; // 总耗时（毫秒）
  pageLoadTime?: number; // 页面加载时间（毫秒）
}

// 检测结果接口
export interface DetectionResult {
  status: DetectionStatus;
  redirectInfo?: RedirectInfo;
  redirectEvents: RedirectEvent[];
  timestamps: Timestamps;
  deviceInfo: DeviceInfo;
  environment?: EnvironmentConfig;
  errors?: ErrorInfo[];
  screenshots?: Screenshots;
  networkRequests?: NetworkRequest[];
  pageLoadTime?: number;
}

// 截图数据
export interface Screenshots {
  before?: string; // Base64 编码的跳转前截图
  after?: string; // Base64 编码的跳转后截图
}

// 设备信息接口
export interface DeviceInfo {
  type: DeviceType;
  browser: BrowserType;
  userAgent: string;
  viewport: ViewportSize;
  name: string;
}

// 设备类型
export type DeviceType = 'mobile' | 'tablet' | 'desktop';

// 浏览器类型
export type BrowserType = 'chrome' | 'safari' | 'firefox';

// 视口尺寸
export interface ViewportSize {
  width: number;
  height: number;
}

// 网络请求信息
export interface NetworkRequest {
  url: string;
  method: string;
  status: number;
  timestamp: number;
  headers?: Record<string, string>;
}

// 环境配置
export interface EnvironmentConfig {
  geolocation?: GeolocationConfig;
  timezone?: string;
  locale?: string;
  networkCondition?: NetworkCondition;
}

// 地理位置配置
export interface GeolocationConfig {
  latitude: number;
  longitude: number;
  accuracy?: number;
}

// 网络条件
export interface NetworkCondition {
  offline: boolean;
  downloadThroughput: number;
  uploadThroughput: number;
  latency: number;
}
