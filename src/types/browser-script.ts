/**
 * 浏览器相关类型定义
 * 包括设备配置、浏览器脚本等
 */

import type { RedirectEvent } from './detection';

// 浏览器脚本中的跳转检测器接口
export interface BrowserRedirectDetector {
  redirects: RedirectEvent[];
  startTime: number;
  recordRedirect: (type: string, targetUrl: string, sourceCode?: string) => void;
}

// 浏览器脚本函数类型
export type BrowserScriptFunction = string;

// 定时器处理函数类型
export type BrowserTimerHandler = (...args: any[]) => void;

// 浏览器脚本中的Window接口扩展
export interface BrowserWindow {
  __redirectDetector?: BrowserRedirectDetector;
  __notifyRedirect?: (redirect: RedirectEvent) => void;
  location: {
    href: string;
    replace: (url: string) => void;
    assign: (url: string) => void;
  };
  setTimeout: (handler: BrowserTimerHandler, timeout?: number, ...args: any[]) => number;
  setInterval: (handler: BrowserTimerHandler, timeout?: number, ...args: any[]) => number;
  open: (url?: string, target?: string, features?: string) => Window | null;
  document: {
    querySelector: (selector: string) => Element | null;
    querySelectorAll: (selector: string) => NodeList;
    forms: HTMLCollectionOf<HTMLFormElement>;
  };
}

// 导出空对象以确保这是一个模块
export {};
