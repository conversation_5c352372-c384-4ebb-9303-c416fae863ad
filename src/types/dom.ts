/**
 * 浏览器DOM相关的类型定义
 * 用于在浏览器上下文中执行的脚本
 */

import type { RedirectEvent } from './detection';

// 扩展Window接口
declare global {
  interface Window {
    __redirectDetector?: BrowserRedirectDetector;
    __notifyRedirect?: (redirect: RedirectEvent) => void;
  }
}

// 浏览器中的跳转检测器接口
export interface BrowserRedirectDetector {
  redirects: RedirectEvent[];
  startTime: number;
  recordRedirect: (type: string, targetUrl: string, sourceCode?: string) => void;
}

// 浏览器上下文中的函数类型
export type BrowserFunction = (...args: any[]) => any;
export type BrowserCallback = (this: any, ...args: any[]) => any;

// 事件监听器类型
export type EventListenerCallback = (event: Event) => void;

// DOM元素类型扩展
export interface ExtendedHTMLFormElement extends HTMLFormElement {
  action: string;
}

export interface ExtendedHTMLAnchorElement extends HTMLAnchorElement {
  href: string;
}

export interface ExtendedHTMLElement extends HTMLElement {
  tagName: string;
}

export interface ExtendedElement extends Element {
  tagName: string;
  getAttribute: (name: string) => string | null;
}

// 导出空对象以确保这是一个模块
export {};
