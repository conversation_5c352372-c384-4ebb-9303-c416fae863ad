/**
 * 核心业务类型定义
 */

import type { DetectionResult, DeviceInfo, EnvironmentConfig, ErrorInfo } from './detection';

// 检测配置接口
export interface DetectionConfig {
  timeout: Partial<TimeoutConfig>;
  resourceFilter: ResourceFilterConfig;
  concurrent: ConcurrentConfig;
  enableScreenshots: boolean;
  enableNetworkLogging: boolean;
  debugMode: boolean;
}

// 超时配置
export interface TimeoutConfig {
  pageLoad: number; // 页面加载超时（毫秒）
  redirectDetection: number; // 跳转检测超时（毫秒）
  networkIdle: number; // 网络空闲等待时间（毫秒）
  totalTask: number; // 总任务超时（毫秒）
  timerRedirect: number; // 定时器跳转检测超时（毫秒）
  metaRefresh: number; // Meta refresh检测超时（毫秒）
  formSubmit: number; // 表单提交检测超时（毫秒）
}

// 资源过滤配置
export interface ResourceFilterConfig {
  interceptResourceTypes: string[]; // 需要拦截的资源类型
  allowedResourceTypes: string[]; // 允许的资源类型
  domainWhitelist: string[]; // 域名白名单
  blockThirdParty: boolean; // 是否阻止第三方资源
}

// 并发配置
export interface ConcurrentConfig {
  maxConcurrent: number; // 最大并发数
  queueSize: number; // 队列大小
  taskTimeout: number; // 单个任务超时（毫秒）
  retryCount: number; // 重试次数
}

// 检测选项接口
export interface DetectionOptions {
  htmlContent?: string;
  htmlFile?: string;
  url?: string;
  deviceConfig?: DeviceConfig;
  environment?: EnvironmentConfig;
  config?: Partial<DetectionConfig>;
}

// 批量检测选项
export interface BatchDetectionOptions {
  htmlFiles?: string[];
  htmlContents?: string[];
  urls?: string[];
  deviceConfigs?: DeviceConfig[];
  environments?: EnvironmentConfig[];
  concurrency?: number;
  config?: Partial<DetectionConfig>;
}

// 批量检测结果
export interface BatchDetectionResult {
  results: DetectionResult[];
  summary: {
    total: number;
    successful: number;
    failed: number;
    withRedirects: number;
    totalTime: number;
  };
  errors?: ErrorInfo[];
}

// 设备配置接口
export interface DeviceConfig {
  name: string;
  userAgent: string;
  viewport: ViewportSize;
  deviceScaleFactor: number;
  isMobile: boolean;
  hasTouch: boolean;
  defaultBrowserType: 'chromium' | 'firefox' | 'webkit';
}

// 设备名称类型
export type DeviceName = 'iPhone13' | 'iPhone14' | 'iPhone15' | 'iPad' | 'GalaxyS21' | 'Pixel7' | 'Xiaomi13' | 'Chrome' | 'Firefox' | 'Safari';

// 环境配置（兼容旧版本）
export interface Environment {
  locale: string;
  timezone: string;
  geolocation?: Geolocation;
  networkConditions?: NetworkConditions;
}

// 地理位置信息（兼容旧版本）
export interface Geolocation {
  latitude: number;
  longitude: number;
}

// 网络条件配置（兼容旧版本）
export interface NetworkConditions {
  offline: boolean;
  downloadThroughput?: number;
  uploadThroughput?: number;
  latency?: number;
}

// 截图数据
export interface Screenshots {
  before?: string; // Base64 编码的跳转前截图
  after?: string; // Base64 编码的跳转后截图
}

// 服务器配置
export interface ServerConfig {
  port: number;
  host: string;
  staticDir: string;
}

// 文件管理器接口
export interface IFileManager {
  createTempFile(content: string, extension?: string): Promise<string>;
  deleteTempFile(filePath: string): Promise<void>;
  cleanup(): Promise<void>;
}

// 浏览器管理器接口
export interface IBrowserManager {
  createContext(deviceConfig?: DeviceConfig, environment?: EnvironmentConfig): Promise<any>;
  destroyContext(context: any): Promise<void>;
  cleanup(): Promise<void>;
}

// 视口尺寸
export interface ViewportSize {
  width: number;
  height: number;
}

// 检测器接口
export interface IDetector {
  initialize(): Promise<void>;
  destroy(): Promise<void>;
  detect(options: DetectionOptions): Promise<DetectionResult>;
  detectBatch(options: BatchDetectionOptions): Promise<BatchDetectionResult>;
  getConfig(): DetectionConfig;
}

// 队列统计信息
export interface QueueStats {
  pending: number;
  running: number;
  completed: number;
  failed: number;
  totalProcessed: number;
}
