/**
 * 类型定义统一导出
 */

// 导出检测相关类型
export * from './detection';

// 导出核心业务类型（排除重复的类型）
export type {
  DetectionConfig,
  TimeoutConfig,
  ResourceFilterConfig,
  ConcurrentConfig,
  DetectionOptions,
  BatchDetectionOptions,
  BatchDetectionResult,
  DeviceConfig,
  DeviceName,
  Environment,
  Geolocation,
  NetworkConditions,
  Screenshots,
  ServerConfig,
  IFileManager,
  IBrowserManager,
  IDetector,
  QueueStats
} from './core';

// 导出浏览器相关类型
export type {
  BrowserRedirectDetector,
  BrowserScriptFunction,
  BrowserTimerHandler,
  BrowserWindow
} from './browser-script';

// 导出DOM相关类型
export type {
  BrowserFunction,
  BrowserCallback,
  EventListenerCallback,
  ExtendedHTMLFormElement,
  ExtendedHTMLAnchorElement,
  ExtendedHTMLElement,
  ExtendedElement
} from './dom';
