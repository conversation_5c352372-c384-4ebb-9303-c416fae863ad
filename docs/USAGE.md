# 使用说明

## 快速开始

### 1. 安装依赖

```bash
bun install
```

### 2. 运行测试

```bash
# 运行基本测试
bun run test

# 运行简单测试
bun run test:simple

# 运行基本示例
bun run example:basic

# 运行高级示例
bun run example:advanced
```

## API 使用

### 便捷函数

最简单的使用方式是使用便捷函数：

```typescript
import { detectHtmlContent, detectHtmlFile, detectUrl } from './src/core/index';

// 检测HTML内容
const result = await detectHtmlContent(`
<!DOCTYPE html>
<html>
<body>
    <script>
        setTimeout(() => {
            window.location.href = 'https://example.com';
        }, 2000);
    </script>
</body>
</html>`);

// 检测HTML文件
const fileResult = await detectHtmlFile('./test.html');

// 检测URL
const urlResult = await detectUrl('https://example.com');
```

### 检测器实例

对于更复杂的使用场景，建议使用检测器实例：

```typescript
import { createDetector, allDevices, environments } from './src/core/index';

const detector = createDetector({
    timeout: {
        pageLoad: 10000,
        redirectDetection: 6000
    },
    concurrent: {
        maxConcurrent: 3
    }
});

try {
    await detector.initialize();
    
    // 单个检测
    const result = await detector.detect({
        htmlContent: '<html>...</html>',
        deviceConfig: allDevices.iPhone15,
        environment: environments.china
    });
    
    // 批量检测
    const batchResults = await detector.detectBatch({
        htmlFiles: ['test1.html', 'test2.html'],
        deviceConfigs: [allDevices.iPhone15, allDevices.Pixel7]
    });
    
} finally {
    await detector.destroy();
}
```

## 设备配置

### 使用预设设备

```typescript
import { allDevices, getDeviceGroup } from './src/core/index';

// 使用单个设备
const result = await detectHtmlContent(html, {
    deviceConfig: allDevices.iPhone15
});

// 使用设备组
const mobileDevices = getDeviceGroup('mobile');
for (const [name, config] of Object.entries(mobileDevices)) {
    const result = await detectHtmlContent(html, { deviceConfig: config });
}
```

### 创建自定义设备

```typescript
import { createCustomDevice } from './src/core/index';

const customDevice = createCustomDevice(
    'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15',
    { width: 414, height: 896 },
    {
        deviceScaleFactor: 3.0,
        isMobile: true,
        hasTouch: true,
        defaultBrowserType: 'webkit'
    }
);
```

## 环境配置

### 使用预设环境

```typescript
import { environments } from './src/core/index';

const result = await detectHtmlContent(html, {
    environment: environments.china
});
```

### 创建自定义环境

```typescript
import { createCustomEnvironment } from './src/core/index';

const customEnv = createCustomEnvironment('zh-CN', 'Asia/Shanghai', {
    geolocation: { latitude: 39.9042, longitude: 116.4074 },
    networkConditions: {
        offline: false,
        downloadThroughput: 1.5 * 1024 * 1024,
        uploadThroughput: 1 * 1024 * 1024,
        latency: 50
    }
});
```

## 配置选项

### 超时配置

```typescript
const detector = createDetector({
    timeout: {
        pageLoad: 10000,        // 页面加载超时
        redirectDetection: 6000, // 跳转检测超时
        networkIdle: 2000,      // 网络空闲超时
        totalTask: 15000,       // 总任务超时
        timerRedirect: 8000,    // 定时器跳转超时
        metaRefresh: 3000,      // meta refresh 超时
        formSubmit: 5000        // 表单提交超时
    }
});
```

### 资源过滤配置

```typescript
const detector = createDetector({
    resourceFilter: {
        interceptResourceTypes: ['document', 'script', 'xhr', 'fetch'],
        allowedResourceTypes: ['image', 'media', 'font'],
        domainWhitelist: ['*.cdn.com', 'api.example.com'],
        blockThirdParty: true
    }
});
```

### 并发配置

```typescript
const detector = createDetector({
    concurrent: {
        maxConcurrent: 5,       // 最大并发数
        queueSize: 100,         // 队列大小
        taskTimeout: 60000,     // 任务超时
        retryCount: 3           // 重试次数
    }
});
```

## 检测结果处理

### 基本结果处理

```typescript
const result = await detectHtmlContent(html);

if (result.status === 'success') {
    if (result.redirectInfo?.hasRedirect) {
        console.log(`检测到${result.redirectInfo.redirectType}跳转`);
        console.log(`目标URL: ${result.redirectInfo.targetUrl}`);
        console.log(`触发时间: ${result.redirectInfo.triggerTime}ms`);
    } else {
        console.log('未检测到跳转');
    }
} else {
    console.log('检测失败:', result.errors);
}
```

### 批量结果处理

```typescript
const results = await detector.detectBatch({
    htmlFiles: ['test1.html', 'test2.html', 'test3.html']
});

const redirectResults = results.filter(r => r.redirectInfo?.hasRedirect);
console.log(`共检测${results.length}个文件，发现${redirectResults.length}个跳转`);

// 按跳转类型分组
const byType = redirectResults.reduce((acc, result) => {
    const type = result.redirectInfo!.redirectType!;
    acc[type] = (acc[type] || 0) + 1;
    return acc;
}, {} as Record<string, number>);

console.log('跳转类型统计:', byType);
```

## 错误处理

### 捕获和处理错误

```typescript
try {
    const result = await detectHtmlContent(html);
    // 处理结果
} catch (error) {
    if (error.message.includes('timeout')) {
        console.log('检测超时，请增加超时时间');
    } else if (error.message.includes('network')) {
        console.log('网络错误，请检查网络连接');
    } else {
        console.log('检测失败:', error.message);
    }
}
```

### 检查结果中的错误

```typescript
const result = await detectHtmlContent(html);

if (result.errors && result.errors.length > 0) {
    console.log('检测过程中的错误:');
    result.errors.forEach(error => {
        console.log(`- ${error.type}: ${error.message}`);
    });
}
```

## 性能优化

### 调整并发数

```typescript
// 根据系统资源调整并发数
const detector = createDetector({
    concurrent: {
        maxConcurrent: process.env.NODE_ENV === 'production' ? 10 : 3
    }
});
```

### 使用队列管理

```typescript
const detector = createDetector();
await detector.initialize();

// 暂停队列
detector.pauseQueue();

// 添加任务
const tasks = htmlFiles.map(file => 
    detector.detectHtmlFile(file)
);

// 恢复队列
detector.resumeQueue();

// 等待完成
const results = await Promise.all(tasks);
```

### 监控队列状态

```typescript
const stats = detector.getQueueStats();
console.log(`队列状态: ${stats.running}个运行中, ${stats.pending}个等待中`);
```

## 调试

### 启用调试模式

```typescript
const detector = createDetector({
    debugMode: true
});
```

### 使用环境变量

```bash
DEBUG=true bun run test.ts
```

### 查看详细日志

```typescript
const detector = createDetector({
    enableNetworkLogging: true,
    debugMode: true
});
```
