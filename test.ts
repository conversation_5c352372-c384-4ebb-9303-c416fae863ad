/**
 * 修复后的测试文件
 */

import { detectHtmlContent, allDevices } from './src/core/index';

async function runFixedTest() {
  console.log('🔧 开始修复后的测试...\n');

  // 测试1: 定时器跳转 - 这是之前失败的测试
  console.log('测试1: 定时器跳转 (修复后)');
  const timerHtml = `
<!DOCTYPE html>
<html>
<head>
    <title>定时器跳转测试</title>
</head>
<body>
    <h1>页面将在1秒后跳转</h1>
    <script>
        console.log('页面加载完成，设置定时器');
        setTimeout(function() {
            console.log('定时器触发，准备跳转');
            window.location.href = 'https://example.com/timer-test';
        }, 1000);
    </script>
</body>
</html>`;

  try {
    const result1 = await detectHtmlContent(timerHtml, {
      deviceConfig: allDevices.Pixel7
    });
    
    console.log('✅ 测试1结果:');
    console.log(`   状态: ${result1.status}`);
    console.log(`   检测到跳转: ${result1.redirectInfo?.hasRedirect ? '是' : '否'}`);
    console.log(`   跳转类型: ${result1.redirectInfo?.redirectType || '无'}`);
    console.log(`   目标URL: ${result1.redirectInfo?.targetUrl || '无'}`);
    console.log(`   触发时间: ${result1.redirectInfo?.triggerTime || 0}ms`);
    console.log(`   总耗时: ${result1.timestamps.totalTime}ms\n`);
  } catch (error) {
    console.error('❌ 测试1失败:', error);
  }

  // 测试2: 立即跳转
  console.log('测试2: 立即跳转');
  const immediateHtml = `
<!DOCTYPE html>
<html>
<head><title>立即跳转测试</title></head>
<body>
    <h1>立即跳转</h1>
    <script>
        window.location.href = 'https://example.com/immediate';
    </script>
</body>
</html>`;

  try {
    const result2 = await detectHtmlContent(immediateHtml, {
      deviceConfig: allDevices.Chrome
    });
    
    console.log('✅ 测试2结果:');
    console.log(`   状态: ${result2.status}`);
    console.log(`   检测到跳转: ${result2.redirectInfo?.hasRedirect ? '是' : '否'}`);
    console.log(`   跳转类型: ${result2.redirectInfo?.redirectType || '无'}`);
    console.log(`   总耗时: ${result2.timestamps.totalTime}ms\n`);
  } catch (error) {
    console.error('❌ 测试2失败:', error);
  }

  // 测试3: 无跳转页面
  console.log('测试3: 无跳转页面');
  const normalHtml = `
<!DOCTYPE html>
<html>
<head><title>正常页面</title></head>
<body>
    <h1>这是一个正常的页面</h1>
    <p>没有任何跳转逻辑</p>
</body>
</html>`;

  try {
    const result3 = await detectHtmlContent(normalHtml, {
      deviceConfig: allDevices.Chrome
    });
    
    console.log('✅ 测试3结果:');
    console.log(`   状态: ${result3.status}`);
    console.log(`   检测到跳转: ${result3.redirectInfo?.hasRedirect ? '是' : '否'}`);
    console.log(`   总耗时: ${result3.timestamps.totalTime}ms\n`);
  } catch (error) {
    console.error('❌ 测试3失败:', error);
  }

  console.log('🏁 测试完成！程序应该正常退出。');
}

// 运行测试
if (import.meta.main) {
  runFixedTest().catch(console.error);
}
