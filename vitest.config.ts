import { defineConfig } from 'vitest/config';
import tsconfigPaths from 'vite-tsconfig-paths';

export default defineConfig({
  plugins: [tsconfigPaths()],
  define: {
    // 为测试环境定义全局变量
    'process.env.NODE_ENV': '"test"'
  },
  test: {
    // 测试环境
    environment: 'node',
    
    // 测试文件匹配模式
    include: [
      '__tests__/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
      'src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'
    ],
    
    // 排除的文件
    exclude: [
      'node_modules',
      'dist',
      '.idea',
      '.git',
      '.cache'
    ],
    
    // 全局设置
    globals: true,
    
    // 超时设置
    testTimeout: 30000,
    hookTimeout: 30000,
    
    // 并发设置
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false,
        maxThreads: 4,
        minThreads: 1
      }
    },
    
    // 覆盖率设置
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        '__tests__/',
        'dist/',
        '*.config.*',
        'examples/',
        'docs/'
      ]
    },
    
    // 报告器
    reporters: ['verbose', 'json', 'html'],
    
    // 输出目录
    outputFile: {
      json: '__tests__/results/test-results.json',
      html: '__tests__/results/test-results.html'
    }
  },
  
  // 解析配置
  resolve: {
    alias: {
      '@': './src',
      '@tests': './__tests__'
    }
  }
});
