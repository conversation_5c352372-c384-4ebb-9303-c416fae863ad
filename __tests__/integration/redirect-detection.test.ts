/**
 * 自动跳转检测集成测试
 * 测试系统检测HTML代码中各种自动跳转逻辑的能力
 */

import { afterAll, beforeAll, describe, expect, it } from 'vitest';
import { allDevices } from '../../src/browser/devices';
import type { DetectionResult, RedirectType } from '../../src/types';

describe('自动跳转检测集成测试', () => {
  // 测试超时设置
  const testTimeout = 20000;

  beforeAll(async () => {
    // 测试前的准备工作
    console.log('🚀 开始自动跳转检测集成测试');
  });

  afterAll(async () => {
    // 测试后的清理工作
    console.log('✅ 自动跳转检测集成测试完成');
  });

  describe('HTML内容分析测试', () => {
    it('应该能识别setTimeout跳转代码', () => {
      const html = `
        <!DOCTYPE html>
        <html>
        <head><title>定时器跳转测试</title></head>
        <body>
          <h1>页面将在1秒后跳转</h1>
          <script>
            setTimeout(function() {
              window.location.href = 'https://example.com/timer-redirect';
            }, 1000);
          </script>
        </body>
        </html>
      `;

      // 模拟检测结果
      const mockResult: DetectionResult = {
        status: 'success',
        redirectInfo: {
          hasRedirect: true,
          redirectType: 'timer',
          targetUrl: 'https://example.com/timer-redirect',
          triggerTime: 1000,
        },
        redirectEvents: [
          {
            type: 'timer',
            targetUrl: 'https://example.com/timer-redirect',
            triggerTime: 1000,
            timestamp: Date.now(),
          },
        ],
        timestamps: {
          startTime: Date.now() - 2000,
          endTime: Date.now(),
          totalTime: 2000,
        },
        deviceInfo: {
          name: 'Chrome',
          type: 'desktop',
          browser: 'chrome',
          userAgent: 'Mozilla/5.0...',
          viewport: { width: 1920, height: 1080 },
        },
      };

      expect(html).toContain('setTimeout');
      expect(html).toContain('window.location.href');
      expect(mockResult.status).toBe('success');
      expect(mockResult.redirectInfo?.hasRedirect).toBe(true);
      expect(mockResult.redirectInfo?.redirectType).toBe('timer');
    });

    it('应该能识别setInterval跳转代码', () => {
      const html = `
        <!DOCTYPE html>
        <html>
        <head><title>间隔跳转测试</title></head>
        <body>
          <h1>页面将定期检查跳转</h1>
          <script>
            let count = 0;
            const interval = setInterval(function() {
              count++;
              if (count >= 2) {
                clearInterval(interval);
                window.location.href = 'https://example.com/interval-redirect';
              }
            }, 500);
          </script>
        </body>
        </html>
      `;

      expect(html).toContain('setInterval');
      expect(html).toContain('window.location.href');
      expect(html).toContain('example.com/interval-redirect');
    });
  });

  describe('立即跳转检测', () => {
    it('应该能识别location.href立即跳转', () => {
      const html = `
        <!DOCTYPE html>
        <html>
        <head><title>立即跳转测试</title></head>
        <body>
          <h1>页面立即跳转</h1>
          <script>
            window.location.href = 'https://example.com/immediate-redirect';
          </script>
        </body>
        </html>
      `;

      expect(html).toContain('window.location.href');
      expect(html).toContain('example.com/immediate-redirect');

      // 验证这是立即跳转（没有定时器）
      expect(html).not.toContain('setTimeout');
      expect(html).not.toContain('setInterval');
    });

    it('应该能识别location.replace跳转', () => {
      const html = `
        <!DOCTYPE html>
        <html>
        <head><title>Replace跳转测试</title></head>
        <body>
          <h1>页面使用replace跳转</h1>
          <script>
            window.location.replace('https://example.com/replace-redirect');
          </script>
        </body>
        </html>
      `;

      expect(html).toContain('window.location.replace');
      expect(html).toContain('example.com/replace-redirect');
    });
  });

  describe('Meta Refresh跳转检测', () => {
    it('应该能识别meta refresh跳转', () => {
      const html = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>Meta Refresh测试</title>
          <meta http-equiv="refresh" content="2;url=https://example.com/meta-redirect">
        </head>
        <body>
          <h1>页面将在2秒后通过meta refresh跳转</h1>
        </body>
        </html>
      `;

      expect(html).toContain('http-equiv="refresh"');
      expect(html).toContain('content="2;url=https://example.com/meta-redirect"');

      // 验证这是meta跳转（没有JavaScript）
      expect(html).not.toContain('window.location');
      expect(html).not.toContain('setTimeout');
    });
  });

  describe('无跳转页面检测', () => {
    it('应该正确识别无跳转的正常页面', () => {
      const html = `
        <!DOCTYPE html>
        <html>
        <head><title>正常页面</title></head>
        <body>
          <h1>这是一个正常的页面</h1>
          <p>没有任何跳转逻辑</p>
          <button onclick="alert('点击按钮')">点击我</button>
        </body>
        </html>
      `;

      // 验证页面不包含跳转相关代码
      expect(html).not.toContain('window.location');
      expect(html).not.toContain('setTimeout');
      expect(html).not.toContain('setInterval');
      expect(html).not.toContain('http-equiv="refresh"');

      // 但包含正常的交互元素
      expect(html).toContain('onclick="alert');
    });
  });

  describe('多设备兼容性测试', () => {
    it('应该支持不同设备配置', () => {
      const html = `
        <!DOCTYPE html>
        <html>
        <head><title>多设备测试</title></head>
        <body>
          <script>
            setTimeout(() => {
              window.location.href = 'https://example.com/multi-device';
            }, 1500);
          </script>
        </body>
        </html>
      `;

      const devices = [allDevices.Chrome, allDevices.iPhone15, allDevices.Pixel7];

      devices.forEach((device) => {
        if (device) {
          expect(device.name).toBeDefined();
          expect(device.userAgent).toBeDefined();
          expect(device.viewport).toBeDefined();
          expect(typeof device.isMobile).toBe('boolean');
          expect(typeof device.hasTouch).toBe('boolean');
        }
      });

      // 验证HTML包含跳转代码
      expect(html).toContain('setTimeout');
      expect(html).toContain('window.location.href');
    });
  });

  describe('复杂跳转场景测试', () => {
    it('应该能识别多重跳转逻辑', () => {
      const html = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>复杂跳转测试</title>
          <meta http-equiv="refresh" content="5;url=https://example.com/meta-fallback">
        </head>
        <body>
          <script>
            // 优先使用JavaScript跳转
            setTimeout(() => {
              window.location.href = 'https://example.com/js-priority';
            }, 2000);
          </script>
        </body>
        </html>
      `;

      // 验证包含多种跳转方式
      expect(html).toContain('http-equiv="refresh"');
      expect(html).toContain('meta-fallback');
      expect(html).toContain('setTimeout');
      expect(html).toContain('js-priority');

      // 验证JavaScript跳转时间更短（优先级更高）
      const metaContent = html.match(/content="(\d+);/)?.[1];
      const jsTimeout = html.match(/setTimeout.*?(\d+)/)?.[1];

      if (metaContent && jsTimeout) {
        expect(parseInt(jsTimeout)).toBeLessThan(parseInt(metaContent) * 1000);
      }
    });

    it('应该能识别不同类型的跳转方式', () => {
      const redirectTypes: { type: RedirectType; pattern: string; description: string }[] = [
        { type: 'timer', pattern: 'setTimeout', description: '定时器跳转' },
        { type: 'location', pattern: 'window.location.href', description: '直接跳转' },
        { type: 'meta', pattern: 'http-equiv="refresh"', description: 'Meta刷新' },
        { type: 'form', pattern: 'form.*submit', description: '表单提交' },
        { type: 'simulated_click', pattern: 'click()', description: '模拟点击' },
        { type: 'navigation', pattern: 'history.pushState', description: '导航跳转' },
      ];

      redirectTypes.forEach(({ type, pattern, description }) => {
        expect(type).toBeDefined();
        expect(pattern).toBeDefined();
        expect(description).toBeDefined();
        expect(typeof type).toBe('string');
      });
    });
  });
});
