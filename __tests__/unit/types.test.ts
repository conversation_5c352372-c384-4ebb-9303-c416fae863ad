/**
 * 类型定义测试
 * 确保类型系统的正确性和一致性
 */

import { describe, expect, it } from 'vitest';
import type {
  BatchDetectionOptions,
  DetectionConfig,
  DetectionOptions,
  DetectionResult,
  DetectionStatus,
  DeviceConfig,
  ErrorType,
  RedirectEvent,
  RedirectInfo,
  RedirectType,
} from '../../src/types';

describe('类型定义测试', () => {
  describe('基础类型', () => {
    it('RedirectType 应该包含所有预期的跳转类型', () => {
      const validTypes: RedirectType[] = [
        'timer',
        'location',
        'meta',
        'form',
        'simulated_click',
        'navigation',
      ];

      // 类型检查在编译时进行，这里主要测试类型值的有效性
      validTypes.forEach((type) => {
        expect(typeof type).toBe('string');
        expect(type.length).toBeGreaterThan(0);
      });
    });

    it('DetectionStatus 应该包含所有预期的状态', () => {
      const validStatuses: DetectionStatus[] = ['success', 'failed', 'timeout'];

      validStatuses.forEach((status) => {
        expect(typeof status).toBe('string');
        expect(status.length).toBeGreaterThan(0);
      });
    });

    it('ErrorType 应该包含所有预期的错误类型', () => {
      const validErrorTypes: ErrorType[] = [
        'NETWORK_ERROR',
        'TIMEOUT_ERROR',
        'PARSE_ERROR',
        'BROWSER_ERROR',
        'VALIDATION_ERROR',
        'DETECTION_ERROR',
        'DYNAMIC_CODE_ERROR',
      ];

      validErrorTypes.forEach((errorType) => {
        expect(typeof errorType).toBe('string');
        expect(errorType.length).toBeGreaterThan(0);
      });
    });
  });

  describe('接口结构测试', () => {
    it('RedirectEvent 接口应该具有正确的结构', () => {
      const mockRedirectEvent: RedirectEvent = {
        type: 'timer',
        targetUrl: 'https://example.com',
        triggerTime: 1000,
        timestamp: Date.now(),
        sourceCode: 'setTimeout(() => window.location.href = "https://example.com", 1000)',
        stack: 'Error stack trace',
      };

      expect(mockRedirectEvent.type).toBe('timer');
      expect(mockRedirectEvent.targetUrl).toBe('https://example.com');
      expect(typeof mockRedirectEvent.triggerTime).toBe('number');
      expect(typeof mockRedirectEvent.timestamp).toBe('number');
      expect(typeof mockRedirectEvent.sourceCode).toBe('string');
      expect(typeof mockRedirectEvent.stack).toBe('string');
    });

    it('RedirectInfo 接口应该具有正确的结构', () => {
      const mockRedirectInfo: RedirectInfo = {
        hasRedirect: true,
        redirectType: 'location',
        targetUrl: 'https://example.com',
        triggerTime: 500,
        redirectDelay: 1000,
        sourceCode: 'window.location.href = "https://example.com"',
      };

      expect(mockRedirectInfo.hasRedirect).toBe(true);
      expect(mockRedirectInfo.redirectType).toBe('location');
      expect(mockRedirectInfo.targetUrl).toBe('https://example.com');
      expect(typeof mockRedirectInfo.triggerTime).toBe('number');
      expect(typeof mockRedirectInfo.redirectDelay).toBe('number');
      expect(typeof mockRedirectInfo.sourceCode).toBe('string');
    });

    it('DetectionResult 接口应该具有正确的结构', () => {
      const mockDetectionResult: DetectionResult = {
        status: 'success',
        redirectInfo: {
          hasRedirect: true,
          redirectType: 'timer',
          targetUrl: 'https://example.com',
          triggerTime: 1000,
        },
        redirectEvents: [
          {
            type: 'timer',
            targetUrl: 'https://example.com',
            triggerTime: 1000,
            timestamp: Date.now(),
          },
        ],
        timestamps: {
          startTime: Date.now() - 5000,
          endTime: Date.now(),
          totalTime: 5000,
          pageLoadTime: 2000,
        },
        deviceInfo: {
          name: 'Chrome',
          type: 'desktop',
          browser: 'chrome',
          userAgent: 'Mozilla/5.0...',
          viewport: { width: 1920, height: 1080 },
        },
        environment: {
          locale: 'zh-CN',
          timezone: 'Asia/Shanghai',
        },
        errors: [
          {
            type: 'NETWORK_ERROR',
            message: 'Network request failed',
            timestamp: Date.now(),
          },
        ],
        networkRequests: [
          {
            url: 'https://example.com/api',
            method: 'GET',
            status: 200,
            timestamp: Date.now(),
            headers: {
              'Content-Type': 'application/json',
            },
          },
        ],
      };

      expect(mockDetectionResult.status).toBe('success');
      expect(mockDetectionResult.redirectInfo?.hasRedirect).toBe(true);
      expect(Array.isArray(mockDetectionResult.redirectEvents)).toBe(true);
      expect(mockDetectionResult.timestamps).toBeDefined();
      expect(mockDetectionResult.deviceInfo).toBeDefined();
    });
  });

  describe('配置接口测试', () => {
    it('DetectionConfig 接口应该具有正确的结构', () => {
      const mockConfig: DetectionConfig = {
        timeout: {
          pageLoad: 5000,
          redirectDetection: 3000,
          networkIdle: 1000,
          totalTask: 10000,
          timerRedirect: 4000,
          metaRefresh: 2000,
          formSubmit: 3000,
        },
        resourceFilter: {
          interceptResourceTypes: ['image', 'stylesheet'],
          allowedResourceTypes: ['document', 'script'],
          domainWhitelist: ['example.com'],
          blockThirdParty: true,
        },
        concurrent: {
          maxConcurrent: 3,
          queueSize: 10,
          taskTimeout: 15000,
          retryCount: 2,
        },
        enableScreenshots: true,
        enableNetworkLogging: true,
        debugMode: false,
      };

      expect(mockConfig.timeout.pageLoad).toBe(5000);
      expect(mockConfig.resourceFilter.blockThirdParty).toBe(true);
      expect(mockConfig.concurrent.maxConcurrent).toBe(3);
      expect(mockConfig.enableScreenshots).toBe(true);
    });

    it('DeviceConfig 接口应该具有正确的结构', () => {
      const mockDeviceConfig: DeviceConfig = {
        name: 'iPhone15',
        userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X)...',
        viewport: { width: 393, height: 852 },
        deviceScaleFactor: 3.0,
        isMobile: true,
        hasTouch: true,
        defaultBrowserType: 'webkit',
      };

      expect(mockDeviceConfig.name).toBe('iPhone15');
      expect(mockDeviceConfig.isMobile).toBe(true);
      expect(mockDeviceConfig.hasTouch).toBe(true);
      expect(mockDeviceConfig.defaultBrowserType).toBe('webkit');
      expect(mockDeviceConfig.viewport.width).toBe(393);
    });
  });

  describe('选项接口测试', () => {
    it('DetectionOptions 接口应该支持各种配置组合', () => {
      // HTML内容检测选项
      const htmlOptions: DetectionOptions = {
        htmlContent: '<html><body>Test</body></html>',
        deviceConfig: {
          name: 'Chrome',
          userAgent: 'Mozilla/5.0...',
          viewport: { width: 1920, height: 1080 },
          deviceScaleFactor: 1.0,
          isMobile: false,
          hasTouch: false,
          defaultBrowserType: 'chromium',
        },
      };

      // URL检测选项
      const urlOptions: DetectionOptions = {
        url: 'https://example.com',
        config: {
          timeout: {
            pageLoad: 8000,
            redirectDetection: 5000,
          },
        },
      };

      // 文件检测选项
      const fileOptions: DetectionOptions = {
        htmlFile: '/path/to/test.html',
        config: {
          enableScreenshots: true,
          debugMode: true,
        },
      };

      expect(htmlOptions.htmlContent).toBeDefined();
      expect(urlOptions.url).toBeDefined();
      expect(fileOptions.htmlFile).toBeDefined();
    });

    it('BatchDetectionOptions 接口应该支持批量检测配置', () => {
      const batchOptions: BatchDetectionOptions = {
        htmlFiles: ['/path/to/test1.html', '/path/to/test2.html'],
        urls: ['https://example1.com', 'https://example2.com'],
        deviceConfigs: [
          {
            name: 'Chrome',
            userAgent: 'Mozilla/5.0...',
            viewport: { width: 1920, height: 1080 },
            deviceScaleFactor: 1.0,
            isMobile: false,
            hasTouch: false,
            defaultBrowserType: 'chromium',
          },
        ],
        environments: [
          {
            locale: 'zh-CN',
            timezone: 'Asia/Shanghai',
          },
        ],
        config: {
          enableNetworkLogging: true,
        },
      };

      expect(Array.isArray(batchOptions.htmlFiles)).toBe(true);
      expect(Array.isArray(batchOptions.urls)).toBe(true);
      expect(Array.isArray(batchOptions.deviceConfigs)).toBe(true);
      expect(Array.isArray(batchOptions.environments)).toBe(true);
    });
  });
});
