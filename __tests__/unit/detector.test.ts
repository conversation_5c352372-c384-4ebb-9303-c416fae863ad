/**
 * 检测器单元测试
 */

import { describe, it, expect } from 'vitest';
import { allDevices } from '../../src/browser/devices';
import type { DetectionConfig, DeviceConfig } from '../../src/types';

describe('AutoJumpDetector 单元测试', () => {

  describe('设备配置测试', () => {
    it('应该包含所有预期的设备配置', () => {
      expect(allDevices).toBeDefined();
      expect(allDevices.Chrome).toBeDefined();
      expect(allDevices.iPhone15).toBeDefined();
      expect(allDevices.Safari).toBeDefined();

      // 检查设备配置的结构
      const chromeDevice = allDevices.Chrome;
      expect(chromeDevice?.name).toBe('Chrome');
      expect(chromeDevice?.userAgent).toBeDefined();
      expect(chromeDevice?.viewport).toBeDefined();
      expect(typeof chromeDevice?.deviceScaleFactor).toBe('number');
      expect(typeof chromeDevice?.isMobile).toBe('boolean');
      expect(typeof chromeDevice?.hasTouch).toBe('boolean');
      expect(chromeDevice?.defaultBrowserType).toBeDefined();
    });

    it('应该包含移动设备配置', () => {
      const mobileDevices = [allDevices.iPhone15, allDevices.Pixel7, allDevices.GalaxyS21];

      mobileDevices.forEach(device => {
        if (device) {
          expect(device.isMobile).toBe(true);
          expect(device.hasTouch).toBe(true);
          expect(device.viewport.width).toBeGreaterThan(0);
          expect(device.viewport.height).toBeGreaterThan(0);
        }
      });
    });

    it('应该包含桌面设备配置', () => {
      const desktopDevices = [allDevices.Chrome, allDevices.Firefox, allDevices.Safari];

      desktopDevices.forEach(device => {
        if (device) {
          expect(device.isMobile).toBe(false);
          expect(device.viewport.width).toBeGreaterThanOrEqual(1920);
          expect(device.viewport.height).toBeGreaterThanOrEqual(1080);
        }
      });
    });
  });

  describe('配置验证测试', () => {
    it('应该能创建有效的检测配置', () => {
      const config: DetectionConfig = {
        timeout: {
          pageLoad: 5000,
          redirectDetection: 3000,
          networkIdle: 1000,
          totalTask: 10000,
          timerRedirect: 4000,
          metaRefresh: 2000,
          formSubmit: 3000
        },
        resourceFilter: {
          interceptResourceTypes: ['image', 'stylesheet'],
          allowedResourceTypes: ['document', 'script'],
          domainWhitelist: ['example.com'],
          blockThirdParty: true
        },
        concurrent: {
          maxConcurrent: 3,
          queueSize: 10,
          taskTimeout: 15000,
          retryCount: 2
        },
        enableScreenshots: true,
        enableNetworkLogging: true,
        debugMode: false
      };

      expect(config.timeout.pageLoad).toBe(5000);
      expect(config.resourceFilter.blockThirdParty).toBe(true);
      expect(config.concurrent.maxConcurrent).toBe(3);
      expect(config.enableScreenshots).toBe(true);
    });

    it('应该能创建有效的设备配置', () => {
      const deviceConfig: DeviceConfig = {
        name: 'TestDevice',
        userAgent: 'Mozilla/5.0 (Test Device)',
        viewport: { width: 1920, height: 1080 },
        deviceScaleFactor: 1.0,
        isMobile: false,
        hasTouch: false,
        defaultBrowserType: 'chromium'
      };

      expect(deviceConfig.name).toBe('TestDevice');
      expect(deviceConfig.viewport.width).toBe(1920);
      expect(deviceConfig.isMobile).toBe(false);
      expect(deviceConfig.defaultBrowserType).toBe('chromium');
    });
  });

  describe('类型安全测试', () => {
    it('应该正确处理可选的设备配置', () => {
      const partialDevice: Partial<DeviceConfig> = {
        name: 'PartialDevice',
        viewport: { width: 800, height: 600 }
      };

      expect(partialDevice.name).toBe('PartialDevice');
      expect(partialDevice.viewport?.width).toBe(800);
      expect(partialDevice.userAgent).toBeUndefined();
    });

    it('应该支持设备配置的类型检查', () => {
      const device = allDevices.Chrome;

      if (device) {
        // TypeScript 类型检查应该通过
        const name: string = device.name;
        const isMobile: boolean = device.isMobile;
        const browserType: 'chromium' | 'firefox' | 'webkit' = device.defaultBrowserType;

        expect(typeof name).toBe('string');
        expect(typeof isMobile).toBe('boolean');
        expect(['chromium', 'firefox', 'webkit']).toContain(browserType);
      }
    });
  });
});
